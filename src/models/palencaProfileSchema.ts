import mongoose, { Document, Schema } from 'mongoose';

interface IdsInfo {
  type: string | null;
  name: string | null;
  value: string | null;
}

interface BankInfo {
  account_number: string | null;
  holder_name: string | null;
  holder_address: string | null;
  bank_name: string | null;
}

interface VehicleInfo {
  type: string | null;
  brand: string | null;
  model: string | null;
  year: string | null; // API returns year as string
  license_plate: string | null;
  vin: string | null;
}

interface MetricsInfo {
  acceptance_rate: number | null;
  cancellation_rate: number | null;
  rating: number | null;
  lifetime_trips: number | null;
  time_since_first_trip: string | null;
  level_name: string | null;
  debt_pending: number | null;
  debt_paid: number | null;
  activation_status: string | null;
}

export interface Profile extends Document {
  // Top-level fields
  account_id: string;
  external_id: string | null;
  platform: string;

  // Profile section
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  birthday: Date | null;
  phone: string | null;
  address: string | null;
  city_name: string | null;
  picture_url: string | null;
  is_cron_processed: boolean;
  user_id: string | null;
  // IDs Info
  ids_info: IdsInfo[];

  // Bank Info
  bank_info: BankInfo | null;

  // Vehicle Info
  vehicle_info: VehicleInfo;

  // Metrics Info
  metrics_info: MetricsInfo;
}

const palencaProfileSchema = new Schema<Profile>({
  account_id: { type: String, required: true, unique: true },
  external_id: { type: String, default: null },
  platform: { type: String, required: true },

  // Profile Section
  first_name: { type: String, default: null },
  last_name: { type: String, default: null },
  email: { type: String, default: null },
  birthday: { type: Date, default: null },
  phone: { type: String, default: null },
  address: { type: String, default: null },
  city_name: { type: String, default: null },
  picture_url: { type: String, default: null },
  is_cron_processed: { type: Boolean, default: false },
  user_id: { type: String, default: null },

  // IDs Info
  ids_info: [
    {
      type: { type: String, default: null },
      name: { type: String, default: null },
      value: { type: String, default: null },
    },
  ],

  // Bank Info
  bank_info: {
    account_number: { type: String, default: null },
    holder_name: { type: String, default: null },
    holder_address: { type: String, default: null },
    bank_name: { type: String, default: null },
  },

  // Vehicle Info
  vehicle_info: {
    type: { type: String, default: null },
    brand: { type: String, default: null },
    model: { type: String, default: null },
    year: { type: String, default: null },
    license_plate: { type: String, default: null },
    vin: { type: String, default: null },
  },

  // Metrics Info
  metrics_info: {
    acceptance_rate: { type: Number, default: null },
    cancellation_rate: { type: Number, default: null },
    rating: { type: Number, default: null },
    lifetime_trips: { type: Number, default: null },
    time_since_first_trip: { type: String, default: null },
    level_name: { type: String, default: null },
    debt_pending: { type: Number, default: null },
    debt_paid: { type: Number, default: null },
    activation_status: { type: String, default: null },
  },
});

export const PalencaProfile = mongoose.model<Profile>('PalencaProfile', palencaProfileSchema);
