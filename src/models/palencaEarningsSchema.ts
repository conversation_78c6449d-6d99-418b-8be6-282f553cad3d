import mongoose, { Document, Schema } from 'mongoose';

interface EarningEntry {
  amount: number;
  currency: string;
  earning_date: Date;
  cash_amount: number | null;
  count_trips: number;
}

interface PalencaEarnings extends Document {
  account_id: string;
  platform: string;
  earnings: EarningEntry[];
}

const earningEntrySchema = new Schema({
  amount: { type: Number },
  currency: { type: String },
  earning_date: { type: Date },
  cash_amount: { type: Number, default: null },
  count_trips: { type: Number },
});

const palencaEarningsSchema = new Schema<PalencaEarnings>({
  account_id: { type: String, required: true, index: true },
  platform: { type: String, required: true },
  earnings: [earningEntrySchema],
});

export const PalencaEarningsV2 = mongoose.model<PalencaEarnings>('PalencaEarnings', palencaEarningsSchema);
