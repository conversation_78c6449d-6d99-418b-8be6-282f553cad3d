import { Document, model, Schema, Types } from 'mongoose';

export interface RequestNoteMongoI extends Document {
  content: string;
  author: Types.ObjectId; // Reference to User
  admissionRequest?: Types.ObjectId; // Optional link to AdmissionRequest
  createdAt: Date;
  updatedAt: Date;
  entity?: {
    entityType: Entities;
    entityId: Types.ObjectId;
  };
}

export enum Entities {
  AdmissionRequest = 'AdmissionRequest',
  StockVehicle = 'StockVehicle',
}

const noteSchema = new Schema<RequestNoteMongoI>(
  {
    content: {
      type: String,
      required: [true, 'Content is required'],
    },
    author: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Author is required'],
    },
    admissionRequest: {
      type: Schema.Types.ObjectId,
      ref: 'AdmissionRequest',
      required: false,
    },

    entity: {
      entityType: {
        type: String,
        required: false,
        enum: Entities,
      },
      entityId: {
        type: Schema.Types.ObjectId,
        required: false,
        refPath: 'entity.entityType',
      },
    },
  },
  { timestamps: true }
);

export const RequestNoteMongo = model<RequestNoteMongoI>('Note', noteSchema, 'notes');
