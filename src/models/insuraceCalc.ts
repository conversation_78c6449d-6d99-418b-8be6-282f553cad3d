import { Schema, model } from 'mongoose';

const insuranceCalculator = new Schema({
  associatedId: {
    type: Schema.Types.ObjectId,
    ref: 'associateSchema',
  },
  data: {
    age: {
      data: Number,
      factor: Number,
    },
    creditScore: {
      data: Number,
      factor: Number,
    },
    majorViolations: {
      data: Number,
      factor: Number,
    },
    minorViolations: {
      data: Number,
      factor: Number,
    },
    territory: {
      data: String,
      factor: Number,
    },
    msrp: {
      data: Number,
      factor: Number,
    },
  },
  pricePerDay: Number,
  monthlyRate: Number,
});

const InsuranceCalculator = model('InsuranceCalculator', insuranceCalculator);

export default InsuranceCalculator;
