import { Schema, Types, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps'; // ensure this enum is imported

export enum VehicleInspectionStatus {
  PASSED = 'PASSED',
  FAILED = 'FAILED',
}

export interface IVehicleValidationDocument extends Document {
  _id: Types.ObjectId;
  vehicleId: Types.ObjectId;

  physicalPreparation: {
    circulationCardPhoto?: string;
    frontPlatePhoto?: string;
    rearPlatePhoto?: string;
  };

  vehicleReadyForDelivery: {
    wash: boolean;
    clean: boolean;
    qrCodeInPlace: boolean;
  };

  vehicleDeliveredToCustomer: {
    deliveryPhotos360: string[];
    spareTirePhoto?: string;
    batteryPhoto?: string;
    chargerPhoto?: string;
    vinPhoto?: string;
    interiorOdometerPhotos: string[];
    driverWithVehiclePhoto?: string;
    toolsPhoto?: string;
    signedPromissoryNotePhoto?: string;
  };

  // Merged inspection fields
  inspection: {
    status: VehicleInspectionStatus;
    exterior360Photos: {
      frontView: string[];
      rightSide: string[];
      rearView: string[];
      leftSide: string[];
    };
    interiorPhotos: string[];
    odometerPhotos: string[];
    enginePhotos: string[];
    toolsPhotos: string[];
    chargerPhotos: string[];
    spareTirePhotos: string[];
    batteryPhotos: string[];
    issuePhotos: string[];
    issueDescription?: string;
  };

  createdAt: string;
  updatedAt: string;
}

const VehicleValidationDocumentSchema = new Schema<IVehicleValidationDocument>({
  vehicleId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: true,
    unique: true,
    index: true,
  },

  physicalPreparation: {
    circulationCardPhoto: { type: String, default: null },
    frontPlatePhoto: { type: String, default: null },
    rearPlatePhoto: { type: String, default: null },
  },

  vehicleReadyForDelivery: {
    wash: { type: String, default: null },
    clean: { type: String, default: null },
    qrCodeInPlace: { type: String, default: null },
  },

  vehicleDeliveredToCustomer: {
    deliveryPhotos360: { type: [String], default: [] },
    spareTirePhoto: { type: String, default: null },
    batteryPhoto: { type: String, default: null },
    chargerPhoto: { type: String, default: null },
    vinPhoto: { type: String, default: null },
    interiorOdometerPhotos: { type: [String], default: [] },
    driverWithVehiclePhoto: { type: String, default: null },
    toolsPhoto: { type: String, default: null },
    signedPromissoryNotePhoto: { type: String, default: null },
  },

  inspection: {
    status: {
      type: String,
      enum: Object.values(VehicleInspectionStatus),
      required: true,
    },
    exterior360Photos: {
      frontView: { type: [String], default: [] },
      rightSide: { type: [String], default: [] },
      rearView: { type: [String], default: [] },
      leftSide: { type: [String], default: [] },
    },
    interiorPhotos: { type: [String], default: [] },
    odometerPhotos: { type: [String], default: [] },
    enginePhotos: { type: [String], default: [] },
    toolsPhotos: { type: [String], default: [] },
    chargerPhotos: { type: [String], default: [] },
    spareTirePhotos: { type: [String], default: [] },
    batteryPhotos: { type: [String], default: [] },
    issuePhotos: { type: [String], default: [] },
    issueDescription: { type: String, default: null },
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

VehicleValidationDocumentSchema.index({ vehicleId: 1 });

const VehicleValidationDocument = model<IVehicleValidationDocument>(
  'VehicleValidationDocument',
  VehicleValidationDocumentSchema
);

export default VehicleValidationDocument;
