import { AppointmentTypeEnum } from '@/constants';
import mongoose, { Schema, Document } from 'mongoose';

export enum AppointmentStatus {
  scheduled = 'scheduled',
  completed = 'completed',
  canceled = 'canceled',
  noshow = 'noshow',
  /**
   * the below two status are for the appointment status history tracking,
   * these status will not be set as appointment status in database
   */
  rescheduled = 'rescheduled',
  reassigned = 'reassigned',
}
export enum HomeVisitSchedulingActionSource {
  admin_portal = 'admin-portal',
  customer = 'customer',
}

export interface IAppointment extends Document {
  title: string;
  date: Date;
  startTime: Date;
  endTime: Date;
  description: string;
  status: keyof typeof AppointmentStatus;
  duration: number;
  admissionRequestId: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  slot: mongoose.Types.ObjectId;
  rescheduleCount: number;
  meetingLink: string;
  appointmentType?: AppointmentTypeEnum;
  noShow: { count: number; dates: Date[] };
  rescheduleDates: { previousScheduledDate: Date; rescheduleDate: Date }[];
  canceled: { count: number; dates: Date[] };
  previousHomeVisitors: {
    homeVisitorId: mongoose.Types.ObjectId;
    changeDate: Date;
    changeBy: mongoose.Types.ObjectId;
  }[];
  statusHistory: {
    status: keyof typeof AppointmentStatus;
    date: Date;
    actionBy?: mongoose.Types.ObjectId;
    source?: string;
  }[];
}

const AppointmentSchema = new Schema(
  {
    title: { type: String, required: true },
    date: { type: Date, required: true },
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    description: { type: String, default: '' },
    status: {
      type: String,
      enum: Object.values(AppointmentStatus),
      default: AppointmentStatus.scheduled,
    },
    duration: { type: Number, required: true },
    admissionRequestId: { type: Schema.Types.ObjectId, ref: 'AdmissionRequest', required: true },
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    slot: { type: Schema.Types.ObjectId, ref: 'Slot', required: true },
    rescheduleCount: { type: Number, default: 0 },
    meetingLink: { type: String, required: false },
    appointmentType: {
      type: String,
      enum: [AppointmentTypeEnum.HOME_VISIT, AppointmentTypeEnum.VEHICLE_DELIVERY],
      default: AppointmentTypeEnum.HOME_VISIT,
    },
    noShow: {
      count: { type: Number, default: 0 },
      dates: [{ type: Date, default: Date.now }],
    },
    rescheduleDates: [
      {
        previousScheduledDate: { type: Date, required: false },
        /**
         * this will be the date on which the appointment was rescheduled
         */
        rescheduleDate: { type: Date, required: false },
      },
    ],
    canceled: {
      count: { type: Number, default: 0 },
      dates: [{ type: Date, default: Date.now }],
    },

    previousHomeVisitors: [
      {
        homeVisitorId: { type: Schema.Types.ObjectId, ref: 'User', required: false },
        changeDate: { type: Date, required: false },
        changeBy: { type: Schema.Types.ObjectId, ref: 'User', required: false },
      },
    ],

    statusHistory: [
      {
        status: { type: String, enum: Object.values(AppointmentStatus), required: false },
        date: { type: Date, required: false },
        actionBy: { type: Schema.Types.ObjectId, ref: 'User', required: false },
        source: { type: String, required: false },
      },
    ],
  },
  {
    timestamps: true,
  }
);

export const Appointment = mongoose.model<IAppointment>('Appointment', AppointmentSchema);
