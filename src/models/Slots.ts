import mongoose, { Schema, Document } from 'mongoose';
import { ISchedule } from './Schedules';
import { AppointmentTypeEnum } from '@/constants';

export interface ISlots extends Document {
  user: mongoose.Types.ObjectId;
  scheduleId: mongoose.Types.ObjectId | ISchedule;
  date: Date;
  startTime: Date;
  endTime: Date;
  isAvailable: boolean;
  maxAppointments: number;
  currentAppointments: number;
  timezone: string;
  isBlocked: boolean;
  calendarAccess?: AppointmentTypeEnum;
}

const SlotSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    scheduleId: { type: Schema.Types.ObjectId, ref: 'Schedule', required: false },
    date: { type: Date, required: true },
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    isAvailable: { type: Boolean, default: true, required: true },
    maxAppointments: { type: Number, required: true },
    currentAppointments: { type: Number, default: 0 },
    timezone: { type: String, required: true },
    isBlocked: { type: Boolean, default: false },
    calendarAccess: { type: String, enum: Object.values(AppointmentTypeEnum), required: false },
  },
  {
    timestamps: true,
  }
);

/**
 * might need this index when our documents get large
 * but for now, we'll keep it commented out
 */
// SlotSchema.index({ date: 1, startTime: 1, endTime: 1, isAvailable: 1 });

export const Slots = mongoose.model<ISlots>('Slots', SlotSchema);
