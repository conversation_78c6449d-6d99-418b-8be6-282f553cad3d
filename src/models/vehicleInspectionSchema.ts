import mongoose, { Schema, Document, Types } from 'mongoose';

export enum VehicleInspectionStatus {
  PASSED = 'PASSED',
  FAILED = 'FAILED',
}

export enum VehicleInspectionCategory {
  EXTERIOR_360 = 'EXTERIOR_360',
  INTERIOR = 'INTERIOR',
  ODOMETER = 'ODOMETER',
  ENGINE = 'ENGINE',
  TOOLS = 'TOOLS',
  CHARGER = 'CHARGER',
  SPARE_TIRE = 'SPARE_TIRE',
  BATTERY = 'BATTERY',
  ISSUE_PHOTO = 'ISSUE_PHOTO',
}

export interface IVehicleInspection extends Document {
  _id: Types.ObjectId;
  vehicleId: Types.ObjectId;
  status: VehicleInspectionStatus;

  exterior360Photos: {
    frontView: string[];
    rightSide: string[];
    rearView: string[];
    leftSide: string[];
  };

  interiorPhotos: string[];
  odometerPhotos: string[];
  enginePhotos: string[];
  toolsPhotos: string[];
  chargerPhotos: string[];
  spareTirePhotos: string[];
  batteryPhotos: string[];

  issuePhotos: string[];
  issueDescription?: string;

  createdAt: Date;
  updatedAt: Date;
}

const vehicleInspectionSchema = new Schema<IVehicleInspection>(
  {
    vehicleId: {
      type: Schema.Types.ObjectId,
      ref: 'StockVehicle',
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: Object.values(VehicleInspectionStatus),
      required: true,
    },

    exterior360Photos: {
      frontView: { type: [String], default: [] },
      rightSide: { type: [String], default: [] },
      rearView: { type: [String], default: [] },
      leftSide: { type: [String], default: [] },
    },

    interiorPhotos: { type: [String], default: [] },
    odometerPhotos: { type: [String], default: [] },
    enginePhotos: { type: [String], default: [] },
    toolsPhotos: { type: [String], default: [] },
    chargerPhotos: { type: [String], default: [] },
    spareTirePhotos: { type: [String], default: [] },
    batteryPhotos: { type: [String], default: [] },

    issuePhotos: { type: [String], default: [] },
    issueDescription: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    collection: 'vehicleInspections',
  }
);

// Ensure virtuals are included when converting to JSON
vehicleInspectionSchema.set('toJSON', { virtuals: true });
vehicleInspectionSchema.set('toObject', { virtuals: true });

const VehicleInspection = mongoose.model<IVehicleInspection>('VehicleInspection', vehicleInspectionSchema);

export default VehicleInspection;
