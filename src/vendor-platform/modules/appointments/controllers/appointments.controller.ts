import { AsyncController } from '@/types&interfaces/types';
import { AppointmentService } from '../services/appointment.service';

export const getAppointmentById: AsyncController = async (req, res) => {
  const { appointmentId } = req.params;

  const { pAssociate, pStockId, pServiceType } = req.query;

  const appointment = await AppointmentService.getAppointmentById(appointmentId, {
    pAssociate: pAssociate === 'true',
    pStockId: pStockId === 'true',
    pServiceType: pServiceType === 'true',
  });
  return res.status(200).send({ message: 'Appointment found', data: appointment });
};

export const rescheduleAppointment: AsyncController = async (req, res) => {
  const { appointmentId } = req.params;
  const { startTime, serviceTypeId } = req.body;

  const appointment = await AppointmentService.rescheduleAppointment(appointmentId, startTime, serviceTypeId);
  return res.status(200).send({ message: 'Appointment rescheduled', data: appointment });
};

export const cancelAppointment: AsyncController = async (req, res) => {
  const { appointmentId } = req.params;
  const appointment = await AppointmentService.cancelAppointment(appointmentId);
  return res.status(200).send({ message: 'Appointment cancelled', data: appointment });
};

export const confirmAppointment: AsyncController = async (req, res) => {
  const { appointmentId } = req.params;
  await AppointmentService.confirmAppointment(appointmentId);
  return res.status(200).send({ message: 'Appointment confirmed' });
};

export const notAttendedAppointment: AsyncController = async (req, res) => {
  const { appointmentId } = req.params;
  await AppointmentService.notAttendedAppointment(appointmentId);
  return res.status(200).send({ message: 'Appointment not attended' });
};

export const getLastScheduledAppointmentByAssociateId: AsyncController = async (req, res) => {
  const { associateId } = req.params;

  const appointment = await AppointmentService.getLastScheduledAppointmentByAssociateId(associateId);

  return res.status(200).send({ message: 'Appointment found', data: appointment });
};
