import OrganizationModel from '../models/organization.model';
import mongoose from 'mongoose';

const DEFAULT_ORG_ID = '507f1f77bcf86cd799439011';

export async function seedDefaultOrganization(): Promise<void> {
  try {
    console.log('🌱 Verificando organización por defecto...');

    // Verificar si ya existe la organización
    const existingOrg = await OrganizationModel.findById(DEFAULT_ORG_ID);

    if (existingOrg) {
      console.log(`✅ Organización por defecto ya existe: ${existingOrg.name}`);
      return;
    }

    // Crear organización por defecto
    const defaultOrg = new OrganizationModel({
      _id: new mongoose.Types.ObjectId(DEFAULT_ORG_ID),
      name: 'OneCarNow - Organización Principal',
      country: 'mx',
      website: 'https://onecarnow.com',
      users: [],
      globalScheduleConfig: {
        timezone: 'America/Mexico_City',
        workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        workingHours: {
          start: '09:00',
          end: '18:00',
        },
        breakTime: {
          start: '13:00',
          end: '14:00',
        },
        appointmentDuration: 60,
        maxSimultaneousAppointments: 5,
      },
    });

    await defaultOrg.save();
    console.log(`✅ Organización por defecto creada: ${defaultOrg.name}`);
  } catch (error: any) {
    console.error('❌ Error creando organización por defecto:', error.message);
    throw error;
  }
}

export async function getDefaultOrganizationId(): Promise<string> {
  try {
    // Verificar si existe la organización por defecto
    const org = await OrganizationModel.findById(DEFAULT_ORG_ID);

    if (!org) {
      // Si no existe, crearla
      await seedDefaultOrganization();
    }

    return DEFAULT_ORG_ID;
  } catch (error: any) {
    console.error('❌ Error obteniendo organización por defecto:', error.message);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  console.log('🚀 Ejecutando seed de organización por defecto...');
  seedDefaultOrganization()
    .then(() => {
      console.log('✅ Seed completado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error en seed:', error);
      process.exit(1);
    });
}
