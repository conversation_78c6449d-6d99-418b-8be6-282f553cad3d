import { Router } from 'express';
import { VendorVerificationController } from '../controllers/vendor.controller';
import { ValidationMiddleware } from '../middlewares/validation.middleware';

const router = Router();

// Buscar vehículo por placa
router.get('/search-vehicle/:plate', VendorVerificationController.searchVehicle);

// Obtener centros de verificación por estado
router.get('/verification-centers', VendorVerificationController.getVerificationCenters);

// Crear nuevo centro de verificación (para usuarios con permisos de admin)
router.post('/verification-centers', VendorVerificationController.createVerificationCenter);

// Registrar nueva verificación
router.post(
  '/verifications',
  ValidationMiddleware.validatePlateFormat,
  ValidationMiddleware.validateVerificationDate,
  ValidationMiddleware.validateVerificationCenter,
  ValidationMiddleware.validateVendorEvidence,
  ValidationMiddleware.validateDuplicateVerification,
  // ValidationMiddleware.validateVerificationPeriod, // Comentado temporalmente para testing
  VendorVerificationController.createVerification
);

// Obtener verificaciones del centro
router.get('/centers/:centerId/verifications', VendorVerificationController.getCenterVerifications);

// Dashboard general con estadísticas (para vendor platform)
router.get('/dashboard', VendorVerificationController.getDashboard);

// Obtener estadísticas del centro
router.get('/centers/:centerId/stats', VendorVerificationController.getCenterStats);

// Obtener historial de verificaciones por vehículo
router.get(
  '/vehicles/:plate/history',
  ValidationMiddleware.validatePlateFormat,
  VendorVerificationController.getVehicleHistory
);

export default router;
