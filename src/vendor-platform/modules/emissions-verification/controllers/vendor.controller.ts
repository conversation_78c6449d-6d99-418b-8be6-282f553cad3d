import { Request, Response } from 'express';
import { EmissionsVerificationService } from '../services/emissions-verification.service';
import { VerificationCenter } from '../models/verification-center.model';
import { EmissionsVerification } from '../models/emissions-verification.model';
import OrganizationModel from '../../organizations/models/organization.model';
import UserVendorModel from '../../users/models/user.model';
import mongoose from 'mongoose';
import '../types/request.types'; // Importar tipos globales

export class VendorVerificationController {
  /**
   * Buscar vehículo por placa
   */
  static async searchVehicle(req: Request, res: Response) {
    try {
      const { plate } = req.params;

      if (!plate) {
        return res.status(400).json({
          success: false,
          message: 'La placa es requerida',
        });
      }

      const vehicle = await EmissionsVerificationService.searchVehicleByPlate(plate);

      if (!vehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehículo no encontrado en el sistema',
        });
      }

      return res.json({
        success: true,
        data: {
          vehicle,
          exists: true,
        },
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error inesperado',
      });
    }
  }

  /**
   * Obtener centros de verificación por estado (opcional)
   */
  static async getVerificationCenters(req: Request, res: Response) {
    try {
      const { state } = req.query;

      let centers;
      if (state) {
        // Si se proporciona estado, filtrar por estado
        centers = await EmissionsVerificationService.getVerificationCentersByState(state as string);
      } else {
        // Si no se proporciona estado, devolver todos los centros activos
        centers = await VerificationCenter.find({ isActive: true }).sort({ name: 1 });
      }

      console.log('📋 Centros encontrados:', {
        count: centers.length,
        state: state || 'todos',
        centers: centers.map((c) => ({ name: c.name, code: c.code, state: c.state })),
      });

      return res.json({
        success: true,
        data: centers,
      });
    } catch (error) {
      console.error('❌ Error obteniendo centros:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error inesperado',
      });
    }
  }

  /**
   * Registrar nueva verificación
   */
  static async createVerification(req: Request, res: Response) {
    try {
      console.log('🔍 createVerification - Headers:', req.headers);
      console.log('🔍 createVerification - Query params:', req.query);
      console.log('🔍 createVerification - Params:', req.params);
      console.log('🔍 createVerification - Body completo:', JSON.stringify(req.body, null, 2));
      console.log('🔍 createVerification - User vendor:', req.userVendor);
      console.log('🔍 createVerification - User Req:', req.userReq);

      const { vehiclePlate, verificationCenterId, verificationDate, vehiclePhoto, circulationCard } =
        req.body;

      console.log('🔍 createVerification - Datos extraídos:', {
        vehiclePlate: vehiclePlate || 'UNDEFINED',
        verificationCenterId: verificationCenterId || 'UNDEFINED',
        verificationDate: verificationDate || 'UNDEFINED',
        vehiclePhoto: vehiclePhoto ? 'PRESENTE' : 'AUSENTE',
        circulationCard: circulationCard ? 'PRESENTE' : 'AUSENTE',
      });

      // Validaciones
      if (!vehiclePlate || !verificationCenterId || !verificationDate || !vehiclePhoto || !circulationCard) {
        console.log('❌ createVerification - Validación fallida', {
          vehiclePlate,
          verificationCenterId,
          verificationDate,
          vehiclePhoto,
          circulationCard,
        });
        return res.status(400).json({
          success: false,
          message: 'Todos los campos son requeridos',
        });
      }

      // Verificar que el usuario esté autenticado
      const userId = req.userVendor?.userId || req.userReq?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Usuario no autenticado',
        });
      }

      console.log('🔍 createVerification - Llamando al servicio', {
        vehiclePlate,
        verificationCenterId,
        verificationDate: new Date(verificationDate),
        createdBy: userId,
      });

      const verification = await EmissionsVerificationService.createVerification({
        vehiclePlate,
        verificationCenterId,
        verificationDate: new Date(verificationDate),
        vehiclePhoto,
        circulationCard,
        createdBy: userId, // Usuario autenticado del vendor platform
      });

      console.log('✅ createVerification - Verificación creada', { verification });

      return res.status(201).json({
        success: true,
        message: 'Verificación registrada exitosamente',
        data: {
          verification,
          customerLink: `${process.env.CUSTOMER_PORTAL_URL}/verification/${verification.uniqueLink}`,
        },
      });
    } catch (error: any) {
      console.log('❌ createVerification - Error capturado:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        fullError: error,
      });
      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener verificaciones del centro
   */
  static async getCenterVerifications(req: Request, res: Response) {
    try {
      const { centerId } = req.params;
      const { page = 1, limit = 20, status } = req.query;

      const result = await EmissionsVerificationService.getVerificationsByCenter(
        centerId,
        parseInt(page as string),
        parseInt(limit as string),
        status as string
      );

      res.json({
        success: true,
        data: result,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener historial de verificaciones por vehículo
   */
  static async getVehicleHistory(req: Request, res: Response) {
    try {
      const { plate } = req.params;

      if (!plate) {
        return res.status(400).json({
          success: false,
          message: 'La placa es requerida',
        });
      }

      const history = await EmissionsVerificationService.getVehicleVerificationHistory(plate);

      return res.json({
        success: true,
        data: history,
      });
    } catch (error: any) {
      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Dashboard general con estadísticas (para vendor platform)
   */
  static async getDashboard(req: Request, res: Response) {
    try {
      const today = new Date();
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sixMonthsFromNow = new Date(today.getTime() + 180 * 24 * 60 * 60 * 1000);

      const [
        totalVerifications,
        completedVerifications,
        pendingCustomer,
        recentVerifications,
        upcomingDue,
        overdueVerifications,
        totalCenters,
        activeCenters,
      ] = await Promise.all([
        EmissionsVerification.countDocuments(),
        EmissionsVerification.countDocuments({ status: 'completed' }),
        EmissionsVerification.countDocuments({ status: 'pending_customer' }),
        EmissionsVerification.countDocuments({
          createdAt: { $gte: thirtyDaysAgo },
        }),
        EmissionsVerification.countDocuments({
          status: 'completed',
          nextVerificationDate: { $gte: today, $lte: sixMonthsFromNow },
        }),
        EmissionsVerification.countDocuments({
          status: 'completed',
          nextVerificationDate: { $lt: today },
        }),
        VerificationCenter.countDocuments(),
        VerificationCenter.countDocuments({ isActive: true }),
      ]);

      const completionRate =
        totalVerifications > 0 ? Math.round((completedVerifications / totalVerifications) * 100) : 0;

      res.json({
        success: true,
        data: {
          totalVerifications,
          pendingVerifications: pendingCustomer,
          completedThisMonth: recentVerifications,
          averageCompletionTime: 2.5, // Valor por defecto, se puede calcular después
          expiringThisMonth: upcomingDue,
          summary: {
            totalVerifications,
            completedVerifications,
            pendingCustomer,
            completionRate,
          },
          recent: {
            last30Days: recentVerifications,
          },
          alerts: {
            upcomingDue,
            overdue: overdueVerifications,
          },
          centers: {
            total: totalCenters,
            active: activeCenters,
          },
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener estadísticas del centro
   */
  static async getCenterStats(req: Request, res: Response) {
    try {
      const { centerId } = req.params;

      // Obtener estadísticas básicas
      const [totalVerifications, pendingCustomer, completed, thisMonth] = await Promise.all([
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1).then((r) => r.pagination.total),
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1, 'pending_customer').then(
          (r) => r.pagination.total
        ),
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1, 'completed').then(
          (r) => r.pagination.total
        ),
        // Verificaciones de este mes
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1000).then((r) => {
          const currentMonth = new Date();
          currentMonth.setDate(1);
          return r.verifications.filter((v) => v.verificationDate >= currentMonth).length;
        }),
      ]);

      res.json({
        success: true,
        data: {
          total: totalVerifications,
          pendingCustomer,
          completed,
          thisMonth,
          completionRate: totalVerifications > 0 ? Math.round((completed / totalVerifications) * 100) : 0,
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Crear nuevo centro de verificación (para usuarios con permisos de admin)
   */
  static async createVerificationCenter(req: Request, res: Response) {
    try {
      // Verificar que el usuario esté autenticado
      if (!req.userVendor?.userId) {
        return res.status(401).json({
          success: false,
          message: 'Usuario no autenticado',
        });
      }

      // Obtener información del usuario para verificar permisos
      const user = await UserVendorModel.findById(req.userVendor.userId);
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Usuario no encontrado',
        });
      }

      // Verificar que el usuario tenga permisos de admin
      if (!['superAdmin', 'company-gestor'].includes(user.userType)) {
        return res.status(403).json({
          success: false,
          message: 'Usuario no autorizado para crear centros de verificación',
        });
      }

      let { name, code, state, organizationId, location, authorizedFor } = req.body;

      console.log('📝 Datos recibidos para crear centro (vendor):', {
        name,
        code,
        state,
        organizationId,
        location,
        authorizedFor,
        userId: req.userVendor.userId,
        userType: user.userType,
      });

      // Validación de campos requeridos
      if (!name || !code || !state || !organizationId || !location) {
        return res.status(400).json({
          success: false,
          message: 'Todos los campos obligatorios son requeridos',
          details: {
            name: !name ? 'Nombre es requerido' : null,
            code: !code ? 'Código es requerido' : null,
            state: !state ? 'Estado es requerido' : null,
            organizationId: !organizationId ? 'ID de organización es requerido' : null,
            location: !location ? 'Ubicación es requerida' : null,
          },
        });
      }

      // Validar que la ubicación tenga los campos requeridos
      if (!location.address || !location.city || !location.state) {
        return res.status(400).json({
          success: false,
          message: 'Los campos de ubicación son requeridos',
          details: {
            address: !location.address ? 'Dirección es requerida' : null,
            city: !location.city ? 'Ciudad es requerida' : null,
            state: !location.state ? 'Estado de ubicación es requerido' : null,
          },
        });
      }

      // Si no se proporciona organizationId o es el ID por defecto, crear/usar organización por defecto
      const DEFAULT_ORG_ID = '507f1f77bcf86cd799439011';
      if (!organizationId || organizationId === DEFAULT_ORG_ID) {
        let org = await OrganizationModel.findById(DEFAULT_ORG_ID);
        if (!org) {
          console.log('🏢 Creando organización por defecto...');
          org = new OrganizationModel({
            _id: new mongoose.Types.ObjectId(DEFAULT_ORG_ID),
            name: 'OneCarNow - Organización Principal',
            country: 'mx',
            website: 'https://onecarnow.com',
            users: [],
          });
          await org.save();
          console.log('✅ Organización por defecto creada');
        }
        organizationId = DEFAULT_ORG_ID;
      }

      const center = new VerificationCenter({
        name,
        code: code.toUpperCase(),
        state: state.toUpperCase(),
        organizationId,
        location: {
          ...location,
          country: location.country || 'mx',
        },
        authorizedFor: authorizedFor || ['gasoline', 'diesel'],
        isActive: true,
      });

      console.log('💾 Guardando centro (vendor):', center);
      await center.save();
      console.log('✅ Centro guardado exitosamente (vendor)');

      return res.status(201).json({
        success: true,
        message: 'Centro de verificación creado exitosamente',
        data: center,
      });
    } catch (error: any) {
      console.error('❌ Error creando centro de verificación (vendor):', error);

      if (error.code === 11000) {
        return res.status(400).json({
          success: false,
          message: 'El código del centro de verificación ya existe',
        });
      }

      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          message: 'Error de validación',
          details: error.errors,
        });
      }

      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
}
