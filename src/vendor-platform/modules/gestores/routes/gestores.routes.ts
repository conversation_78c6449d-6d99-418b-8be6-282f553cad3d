import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import {
  createGestor,
  getAllGestores,
  getGestorById,
  updateGestorById,
} from '../controllers/gestores.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';

const gestoresRouter = Router();

const gestoresURL = '/gestores';

// --- Ruta<PERSON> de Gestores ---
gestoresRouter.get(gestoresURL, verifyTokenVendorPlatform, errorHandlerV2(getAllGestores));
gestoresRouter.get(`${gestoresURL}/:id`, verifyTokenVendorPlatform, errorHandlerV2(getGestorById));
gestoresRouter.post(gestoresURL, verifyTokenVendorPlatform, errorHandlerV2(createGestor));
gestoresRouter.patch(`${gestoresURL}/:id`, verifyTokenVendorPlatform, errorHandlerV2(updateGestorById));

export default gestoresRouter;
