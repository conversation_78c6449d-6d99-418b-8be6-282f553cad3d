import { AsyncController } from '@/types&interfaces/types';
import { correctiveMaintenanceService } from '../services/corrective-maintenance.service';
import { quotationService } from '../services/quotation.service';
import { correctiveMaintenanceNotificationService } from '../services/notification.service';
import {
  CorrectiveMaintenanceType,
  FailureType,
  VehicleArrivalMethod,
  CorrectiveMaintenanceStatus,
} from '../models/corrective-maintenance-order.model';
import { QuotationStatus } from '../models/quotation.model';
import { Workshop } from '../../workshop/models/workshops.model';

// Helper function to transform order for frontend compatibility
function transformOrderForFrontend(order: any) {
  if (!order) return null;

  const orderObj = order.toObject ? order.toObject() : order;
  const transformedOrder = {
    ...orderObj,
    vehicle: orderObj.stockVehicle, // Map stockVehicle to vehicle for frontend
  };

  // Remove the original stockVehicle field to avoid confusion
  delete (transformedOrder as any).stockVehicle;

  return transformedOrder;
}

/**
 * Create a new corrective maintenance order
 */
export const createCorrectiveMaintenanceOrder: AsyncController = async (req, res) => {
  try {
    const {
      stockId,
      associateId,
      workshopId,
      type,
      failureType,
      arrivalMethod,
      customerDescription,
      canVehicleDrive,
      needsTowTruck,
      approvalType,
    } = req.body;

    // Validate required fields
    if (!stockId || !associateId || !workshopId || !type || !failureType || !arrivalMethod) {
      return res.status(400).json({
        message: 'Missing required fields',
        required: ['stockId', 'associateId', 'workshopId', 'type', 'failureType', 'arrivalMethod'],
      });
    }

    // Validate enum values
    if (!Object.values(CorrectiveMaintenanceType).includes(type)) {
      return res.status(400).json({ message: 'Invalid maintenance type' });
    }

    if (!Object.values(FailureType).includes(failureType)) {
      return res.status(400).json({ message: 'Invalid failure type' });
    }

    if (!Object.values(VehicleArrivalMethod).includes(arrivalMethod)) {
      return res.status(400).json({ message: 'Invalid arrival method' });
    }

    const organizationId = req.userVendor.organizationId;

    const order = await correctiveMaintenanceService.createOrder({
      stockId,
      associateId,
      organizationId,
      workshopId,
      type,
      failureType,
      arrivalMethod,
      customerDescription,
      canVehicleDrive: canVehicleDrive ?? arrivalMethod === VehicleArrivalMethod.DRIVING,
      needsTowTruck: needsTowTruck ?? arrivalMethod === VehicleArrivalMethod.TOW_TRUCK,
      approvalType: approvalType || 'fleet',
    });

    // Get workshop info for notifications
    const workshop = await Workshop.findById(workshopId);

    // Send notification
    await correctiveMaintenanceNotificationService.sendMaintenanceRequestNotification(order, {
      workshopName: workshop?.name,
      fleetEmail: '<EMAIL>',
    });

    return res.status(201).json({
      message: 'Corrective maintenance order created successfully',
      data: order,
    });
  } catch (error: any) {
    console.error('Error creating corrective maintenance order:', error);
    return res.status(500).json({
      message: 'Error creating corrective maintenance order',
      error: error.message,
    });
  }
};

/**
 * Complete diagnosis for a corrective maintenance order
 */
export const completeDiagnosis: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { diagnosisNotes, services: servicesString } = req.body; // Cambiamos el nombre para evitar confusión
    const files = req.files as Express.Multer.File[];

    // Parseamos `services` desde string JSON a un array de objetos
    let services: any[] = [];
    try {
      services = JSON.parse(servicesString); // Ahora sí necesitamos JSON.parse
    } catch (parseError) {
      return res.status(400).json({
        message: 'Invalid services format. Must be a valid JSON array.',
      });
    }

    if (!diagnosisNotes || !services || !Array.isArray(services) || services.length === 0) {
      return res.status(400).json({
        message: 'Diagnosis notes and services are required, and services must be a non-empty array',
      });
    }

    const order = await correctiveMaintenanceService.completeDiagnosis(
      orderId,
      { diagnosisNotes, services },
      files
    );

    // Send notification
    await correctiveMaintenanceNotificationService.sendDiagnosisCompletedNotification(order, {
      fleetEmail: '<EMAIL>',
    });

    return res.status(200).json({
      message: 'Diagnosis completed successfully',
      data: order,
    });
  } catch (error: any) {
    console.error('Error completing diagnosis:', error);
    return res.status(500).json({
      message: 'Error completing diagnosis',
      error: error.message,
    });
  }
};

/**
 * Create quotation from diagnosed order
 */
export const createQuotation: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;
    const {
      approvalType,
      approverEmail,
      validityDays,
      customerNotes,
      internalNotes,
      paymentTerms,
      warrantyTerms,
      services: servicesString,
    } = req.body;

    // Parse services JSON if provided as string (from FormData)
    let services = null;
    if (servicesString) {
      try {
        services = typeof servicesString === 'string' ? JSON.parse(servicesString) : servicesString;
      } catch (error) {
        console.error('❌ [CREATE QUOTATION] Error parsing services JSON:', error);
        return res.status(400).json({
          message: 'Invalid services data format',
          error: 'Services must be valid JSON',
        });
      }
    }

    console.log('🎯 [CREATE QUOTATION] Controller received data:', {
      orderId,
      servicesProvided: !!services,
      servicesCount: services?.length || 0,
      rawServicesType: typeof servicesString,
      services: services?.map((s: { serviceId: any; serviceName: any; parts: string | any[] }) => ({
        serviceId: s.serviceId,
        serviceName: s.serviceName,
        partsCount: s.parts?.length || 0,
        parts: s.parts,
      })),
    });

    const quotation = await quotationService.createQuotation({
      orderId,
      approvalType: approvalType || 'fleet',
      approverEmail,
      validityDays,
      customerNotes,
      internalNotes,
      paymentTerms,
      warrantyTerms,
      services,
    });

    return res.status(201).json({
      message: 'Quotation created successfully',
      data: quotation,
    });
  } catch (error: any) {
    console.error('Error creating quotation:', error);
    return res.status(500).json({
      message: 'Error creating quotation',
      error: error.message,
    });
  }
};

/**
 * Submit quotation for approval
 */
export const submitQuotationForApproval: AsyncController = async (req, res) => {
  try {
    const { quotationId } = req.params;

    const quotation = await quotationService.submitForApproval(quotationId);

    // Get order info for notification
    const order = await correctiveMaintenanceService
      .getOrders({
        organizationId: req.userVendor.organizationId,
      })
      .then((result) => result.orders.find((o) => o._id.toString() === quotation.orderId.toString()));

    if (order) {
      await correctiveMaintenanceNotificationService.sendQuotationApprovalNotification(quotation, order, {
        fleetEmail: '<EMAIL>',
      });
    }

    return res.status(200).json({
      message: 'Quotation submitted for approval successfully',
      data: quotation,
    });
  } catch (error: any) {
    console.error('Error submitting quotation for approval:', error);
    return res.status(500).json({
      message: 'Error submitting quotation for approval',
      error: error.message,
    });
  }
};

/**
 * Process approval decisions for quotation services
 */
export const processApprovalDecisions: AsyncController = async (req, res) => {
  try {
    const { quotationId } = req.params;
    const { decisions } = req.body;

    if (!decisions || !Array.isArray(decisions) || decisions.length === 0) {
      return res.status(400).json({
        message: 'Approval decisions are required',
      });
    }

    const quotation = await quotationService.processApprovalDecisions(quotationId, decisions);

    return res.status(200).json({
      message: 'Approval decisions processed successfully',
      data: quotation,
    });
  } catch (error: any) {
    console.error('Error processing approval decisions:', error);
    return res.status(500).json({
      message: 'Error processing approval decisions',
      error: error.message,
    });
  }
};

/**
 * Get corrective maintenance orders
 */
export const getCorrectiveMaintenanceOrders: AsyncController = async (req, res) => {
  try {
    const { workshopId, stockId, associateId, status, type, page, limit, ignoreOrg } = req.query;

    const filters = {
      // Only filter by organizationId if ignoreOrg is not set (for debugging)
      organizationId: ignoreOrg ? undefined : req.userVendor.organizationId,
      workshopId: workshopId as string,
      stockId: stockId as string,
      associateId: associateId as string,
      status: status as CorrectiveMaintenanceStatus,
      type: type as CorrectiveMaintenanceType,
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
    };

    const result = await correctiveMaintenanceService.getOrders(filters);

    // Transform orders to match frontend expectations
    const transformedOrders = result.orders.map((order) => transformOrderForFrontend(order));

    return res.status(200).json({
      message: 'Corrective maintenance orders retrieved successfully',
      data: transformedOrders,
      pagination: result.pagination,
      debug: {
        userOrganizationId: req.userVendor.organizationId,
        filtersApplied: filters,
        ignoreOrgFilter: !!ignoreOrg,
      },
    });
  } catch (error: any) {
    console.error('Error getting corrective maintenance orders:', error);
    return res.status(500).json({
      message: 'Error getting corrective maintenance orders',
      error: error.message,
    });
  }
};

/**
 * Get quotations
 */
export const getQuotations: AsyncController = async (req, res) => {
  try {
    const { orderId, status, approvalType, page, limit } = req.query;

    const filters = {
      orderId: orderId as string,
      status: status as QuotationStatus,
      approvalType: approvalType as 'fleet' | 'customer',
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
    };

    const result = await quotationService.getQuotations(filters);

    return res.status(200).json({
      message: 'Quotations retrieved successfully',
      data: result.quotations,
      pagination: result.pagination,
    });
  } catch (error: any) {
    console.error('Error getting quotations:', error);
    return res.status(500).json({
      message: 'Error getting quotations',
      error: error.message,
    });
  }
};

/**
 * Get corrective maintenance order by ID
 */
export const getCorrectiveMaintenanceOrderById: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;

    // First try to find the specific order by ID and organization
    const order = await correctiveMaintenanceService.getOrderById(orderId, req.userVendor.organizationId);

    if (!order) {
      return res.status(404).json({
        message: 'Corrective maintenance order not found',
      });
    }

    return res.status(200).json({
      message: 'Corrective maintenance order retrieved successfully',
      data: transformOrderForFrontend(order),
    });
  } catch (error: any) {
    console.error('Error getting corrective maintenance order:', error);
    return res.status(500).json({
      message: 'Error getting corrective maintenance order',
      error: error.message,
    });
  }
};

/**
 * Start work on approved services
 */
export const startWork: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;
    const organizationId = req.userVendor.organizationId;

    // Start work on the approved services
    const updatedOrder = await correctiveMaintenanceService.startWork(orderId, organizationId);

    // Send notification about work starting
    await correctiveMaintenanceNotificationService.sendWorkStartedNotification(updatedOrder, {
      workshopName: updatedOrder.workshop?.name,
      fleetEmail: '<EMAIL>',
    });

    return res.status(200).json({
      message: 'Work started successfully',
      data: transformOrderForFrontend(updatedOrder),
    });
  } catch (error: any) {
    console.error('Error starting work:', error);
    return res.status(500).json({
      message: 'Error starting work',
      error: error.message,
    });
  }
};

/**
 * Update service progress
 */
export const updateServiceProgress: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    const organizationId = req.userVendor.organizationId;
    const { status, notes, progressPhotos, actualCost, partsUsed, qualityCheckPassed, technicalNotes } =
      req.body;

    // Handle files from both progressPhotos and evidence fields
    let allFiles: Express.Multer.File[] = [];
    if (req.files) {
      if (Array.isArray(req.files)) {
        // If files is an array (from upload.array)
        allFiles = req.files;
      } else {
        // If files is an object (from upload.fields)
        const filesObj = req.files as { [fieldname: string]: Express.Multer.File[] };
        allFiles = [...(filesObj.progressPhotos || []), ...(filesObj.evidence || [])];
      }
    }

    // Validate status if provided
    if (status && !['in-progress', 'completed', 'waiting-for-parts', 'cancelled'].includes(status)) {
      return res.status(400).json({
        message: 'Invalid status. Must be one of: in-progress, completed, waiting-for-parts, cancelled',
      });
    }

    console.log('🚀 [UPDATE SERVICE PROGRESS] Calling service with files:', allFiles.length);

    // Update service progress
    const updatedService = await correctiveMaintenanceService.updateServiceProgress(
      serviceId,
      organizationId,
      {
        status,
        notes,
        progressPhotos,
        actualCost,
        partsUsed,
        qualityCheckPassed,
        technicalNotes,
      },
      allFiles
    );

    console.log('✅ [UPDATE SERVICE PROGRESS] Service updated successfully');
    console.log(
      '📸 [UPDATE SERVICE PROGRESS] Final progress photos count:',
      updatedService.progressPhotos?.length || 0
    );

    // Send notification if service is completed
    if (status === 'completed') {
      console.log('📧 [UPDATE SERVICE PROGRESS] Sending completion notification...');
      await correctiveMaintenanceNotificationService.sendWorkCompletedNotification(updatedService.orderId, {
        workshopName: updatedService.orderId.workshop?.name,
        fleetEmail: '<EMAIL>',
      });
    }

    console.log('🎉 [UPDATE SERVICE PROGRESS] Request completed successfully');
    return res.status(200).json({
      message: 'Service progress updated successfully',
      data: updatedService,
    });
  } catch (error: any) {
    console.error('Error updating service progress:', error);
    return res.status(500).json({
      message: 'Error al actualizar el progreso del servicio',
      error: error.message,
    });
  }
};

/**
 * Complete corrective maintenance order
 */
export const completeOrder: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;
    const organizationId = req.userVendor.organizationId;
    const {
      completionNotes,
      afterPhotos,
      finalInspectionPassed,
      customerSatisfactionRating,
      totalActualCost,
      workQualityRating,
      recommendationsForFuture,
    } = req.body;

    // Validate ratings if provided
    if (
      customerSatisfactionRating !== undefined &&
      (customerSatisfactionRating < 1 || customerSatisfactionRating > 5)
    ) {
      return res.status(400).json({
        message: 'Customer satisfaction rating must be between 1 and 5',
      });
    }

    if (workQualityRating !== undefined && (workQualityRating < 1 || workQualityRating > 5)) {
      return res.status(400).json({
        message: 'Work quality rating must be between 1 and 5',
      });
    }

    // Complete the order
    const completedOrder = await correctiveMaintenanceService.completeOrder(
      orderId,
      organizationId,
      {
        completionNotes,
        afterPhotos,
        finalInspectionPassed,
        customerSatisfactionRating,
        totalActualCost,
        workQualityRating,
        recommendationsForFuture,
      },
      req.files as Express.Multer.File[]
    );

    // Send completion notifications
    await correctiveMaintenanceNotificationService.sendWorkCompletedNotification(completedOrder, {
      workshopName: completedOrder.workshop?.name,
      fleetEmail: '<EMAIL>',
    });

    return res.status(200).json({
      message: 'Order completed successfully',
      data: transformOrderForFrontend(completedOrder),
    });
  } catch (error: any) {
    console.error('Error completing order:', error);
    return res.status(500).json({
      message: 'Error completing order',
      error: error.message,
    });
  }
};

/**
 * Create appointment for corrective maintenance
 */
export const createCorrectiveMaintenanceAppointment: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { startTime, endTime, serviceTypeId, failureDescription, urgencyLevel } = req.body;

    if (!startTime || !endTime || !serviceTypeId) {
      return res.status(400).json({
        message: 'Start time, end time, and serviceTypeId are required',
      });
    }

    const appointment = await correctiveMaintenanceService.createCorrectiveMaintenanceAppointment(orderId, {
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      serviceTypeId,
      failureDescription,
      urgencyLevel,
    });

    return res.status(201).json({
      message: 'Corrective maintenance appointment created successfully',
      data: appointment,
    });
  } catch (error: any) {
    console.error('Error creating corrective maintenance appointment:', error);
    return res.status(500).json({
      message: 'Error creating corrective maintenance appointment',
      error: error.message,
    });
  }
};

/**
 * Test endpoint to verify routing
 */
export const testEndpoint: AsyncController = async (req, res) => {
  try {
    return res.status(200).json({
      message: 'Test endpoint working',
      timestamp: new Date().toISOString(),
      params: req.params,
      query: req.query,
    });
  } catch (error: any) {
    return res.status(500).json({
      message: 'Test endpoint error',
      error: error.message,
    });
  }
};

/**
 * Get vehicle maintenance history
 */
export const getVehicleMaintenanceHistory: AsyncController = async (req, res) => {
  try {
    const { vehicleId } = req.params;
    const { page, limit } = req.query;
    const organizationId = req.userVendor.organizationId;

    console.log('🔍 Getting vehicle maintenance history:', {
      vehicleId,
      organizationId,
      page,
      limit,
    });

    if (!vehicleId) {
      return res.status(400).json({
        message: 'Vehicle ID is required',
      });
    }

    // Get maintenance history for this vehicle
    console.log('📞 Calling correctiveMaintenanceService.getOrders with filters:', {
      organizationId,
      stockId: vehicleId,
      page: page ? parseInt(page as string) : 1,
      limit: limit ? parseInt(limit as string) : 50,
    });

    const result = await correctiveMaintenanceService.getOrders({
      organizationId,
      stockId: vehicleId,
      page: page ? parseInt(page as string) : 1,
      limit: limit ? parseInt(limit as string) : 50,
    });

    console.log('📊 Orders found:', {
      ordersCount: result.orders.length,
      pagination: result.pagination,
    });

    // Transform orders to include detailed information
    const detailedHistory = await Promise.all(
      result.orders.map(async (order) => {
        console.log('🔄 Processing order:', order._id);

        // Get quotation for each order
        const quotation = await correctiveMaintenanceService.getQuotationByOrderId(order._id.toString());
        console.log('💰 Quotation found for order:', order._id, !!quotation);

        // Transform order with all details
        const transformedOrder = transformOrderForFrontend(order);

        return {
          ...transformedOrder,
          quotation: quotation || null,
        };
      })
    );

    console.log('✅ Detailed history prepared:', {
      historyCount: detailedHistory.length,
    });

    return res.status(200).json({
      message: 'Vehicle maintenance history retrieved successfully',
      data: detailedHistory,
      pagination: result.pagination,
    });
  } catch (error: any) {
    console.error('❌ Error getting vehicle maintenance history:', error);
    return res.status(500).json({
      message: 'Error getting vehicle maintenance history',
      error: error.message,
    });
  }
};

/**
 * Debug endpoint to check database state
 */
export const debugCorrectiveMaintenanceOrders: AsyncController = async (req, res) => {
  try {
    const organizationId = req.userVendor.organizationId;
    const { vehicleId } = req.query;

    console.log('🔍 Debug endpoint called:', { organizationId, vehicleId });

    // Get total count without filters
    const totalResult = await correctiveMaintenanceService.getOrders({});

    // Get count for this organization
    const orgResult = await correctiveMaintenanceService.getOrders({
      organizationId,
    });

    // Get count for specific vehicle if provided
    let vehicleResult = null;
    if (vehicleId) {
      vehicleResult = await correctiveMaintenanceService.getOrders({
        organizationId,
        stockId: vehicleId as string,
      });
    }

    // Get sample orders with more detailed info
    const sampleOrders = totalResult.orders.slice(0, 5).map((order) => ({
      id: order._id,
      organizationId: order.organizationId,
      organizationIdType: typeof order.organizationId,
      organizationIdString: order.organizationId.toString(),
      workshopId: order.workshopId,
      workshopIdType: typeof order.workshopId,
      workshopIdString: order.workshopId.toString(),
      stockId: order.stockId,
      stockIdType: typeof order.stockId,
      stockIdString: order.stockId ? order.stockId.toString() : 'null',
      associateId: order.associateId,
      associateIdType: typeof order.associateId,
      associateIdString: order.associateId ? order.associateId.toString() : 'null',
      status: order.status,
      type: order.type,
      createdAt: order.createdAt,
    }));

    return res.status(200).json({
      message: 'Debug information retrieved',
      data: {
        totalOrdersInDatabase: totalResult.pagination.total,
        ordersForThisOrganization: orgResult.pagination.total,
        ordersForVehicle: vehicleResult ? vehicleResult.pagination.total : 'N/A',
        currentOrganizationId: organizationId,
        currentOrganizationIdType: typeof organizationId,
        requestedVehicleId: vehicleId || 'N/A',
        sampleOrders,
        vehicleOrders: vehicleResult
          ? vehicleResult.orders.map((order) => ({
              id: order._id,
              stockId: order.stockId.toString(),
              status: order.status,
              createdAt: order.createdAt,
            }))
          : [],
        filters: {
          organizationId,
          vehicleId,
        },
        // Check if the organizationId matches any of the sample orders
        organizationMatches: sampleOrders.map((order) => ({
          orderId: order.id,
          matches: order.organizationIdString === organizationId,
          orderOrgId: order.organizationIdString,
          filterOrgId: organizationId,
        })),
      },
    });
  } catch (error: any) {
    console.error('Error in debug endpoint:', error);
    return res.status(500).json({
      message: 'Error getting debug information',
      error: error.message,
    });
  }
};

/**
 * Update service parts status and information
 */
export const updateServiceParts: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    const { parts } = req.body;

    console.log('🔧 [UPDATE SERVICE PARTS] Updating parts for service:', serviceId);
    console.log('📦 [UPDATE SERVICE PARTS] Parts data:', parts);

    // Find and update the service
    const updatedService = await correctiveMaintenanceService.updateServiceParts(serviceId, parts);

    console.log('✅ [UPDATE SERVICE PARTS] Service parts updated successfully');

    return res.status(200).json({
      message: 'Service parts updated successfully',
      data: updatedService,
    });
  } catch (error: any) {
    console.error('❌ Error updating service parts:', error);
    return res.status(500).json({
      message: 'Error al actualizar las refacciones del servicio',
      error: error.message,
    });
  }
};
