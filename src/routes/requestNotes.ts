import express from 'express';
import {
  createNoteController,
  getNotesByUserController,
  getNoteByIdController,
  updateNoteByIdController,
  deleteNoteByIdController,
  getNotesByAdmissionRequestController,
  createAdmissionRequestNoteController,
  getNotes,
} from '../controllers/requestNotes';

const requestNotesRouter = express.Router();

requestNotesRouter.get('/', getNotes);
requestNotesRouter.post('/', createNoteController);
requestNotesRouter.post('/admission-request', createAdmissionRequestNoteController);
requestNotesRouter.get('/', getNotesByUserController);
requestNotesRouter.get('/:id', getNoteByIdController);
requestNotesRouter.put('/:id', updateNoteByIdController);
requestNotesRouter.delete('/:id', deleteNoteByIdController);
requestNotesRouter.get('/admission-request/:admissionRequestId', getNotesByAdmissionRequestController);

export default requestNotesRouter;
