import { Router } from 'express';
import { InsuranceService, factors } from '../services/usInsurance';

const insuranceRouter = Router();

insuranceRouter.post('/calculate', (req, res) => {
  const { age, creditScore, minorViolations, majorViolations, territory, msrp } = req.body;
  const insuranceService = new InsuranceService(factors);
  const day = insuranceService.calculateDailyPremium({
    age,
    creditScore,
    minorViolations,
    majorViolations,
    territory,
    msrp,
  });
  const monthly = insuranceService.calculateMonthlyPremium({
    age,
    creditScore,
    minorViolations,
    majorViolations,
    territory,
    msrp,
  });
  res.json({ day, monthly });
});

export default insuranceRouter;
