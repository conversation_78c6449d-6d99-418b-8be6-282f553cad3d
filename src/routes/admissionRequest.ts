import { Router } from 'express';
import {
  addPreapproval,
  updateRequest,
  getRequestByPhone,
  saveInsuranceCalculator,
} from '../controllers/admissionRequest';

const user = Router();

user.patch('/type', addPreapproval);
user.patch('/update', updateRequest);
user.get('/get-by-phone/:phone', getRequestByPhone);
user.post('/save-insurance-calculator', saveInsuranceCalculator);
export default user;
