import { Router } from 'express';
import {
  createOrUpdateInspection,
  getVehicleInspection,
  getAllVehicleInspections,
} from '../controllers/vehicleInspectionController';

const router = Router();

// Create or update vehicle inspection with images
router.post('/', createOrUpdateInspection);

// Get latest vehicle inspection by vehicle ID
router.get('/:vehicleId', getVehicleInspection);

// Get all vehicle inspections by vehicle ID
router.get('/:vehicleId/history', getAllVehicleInspections);

export default router;
