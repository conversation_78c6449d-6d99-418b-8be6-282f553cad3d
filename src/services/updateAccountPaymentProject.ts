/* eslint-disable max-params */
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../constants/payments-api';
import { wire4Data } from '../constants';
import axios from 'axios';
import StockVehicle from '../models/StockVehicleSchema';
import Associate from '../models/associateSchema';
import AssociatePayments from '../models/associatePayments';
import { createWire4BankAccount, createWire4I80BankAccount } from './createWire4BankAccount';

export async function updateAccountProject(contract: string, subContract: number, fondo: string) {
  const updatePaymentProject = async (clientId: string, monexClabe: string, oldMonexClabe: string) => {
    try {
      const response = await axios.patch(
        `${PAYMENTS_API_URL}/clients/${clientId}`,
        {
          monexClabe,
          metadata: {
            new_account_show_count: 0,
            oldMonexClabe,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.log(error);
      return error;
    }
  };
  const contractData = await StockVehicle.findOne({ carNumber: contract });

  if (!contractData) {
    return 'No se encontró el contrato';
  }

  // Obtener email del conductor

  const lastDriverIndex = subContract === 0 ? contractData.drivers.length - 1 : subContract - 1;
  const associatedData = await Associate.findById(contractData.drivers[lastDriverIndex]._id);
  if (!associatedData) {
    return 'No se encontró el conductor';
  }
  const driverEmail = associatedData.email;
  const driverPhone = associatedData.phone;
  const driverName = associatedData.firstName;
  const associateId = associatedData.clientId;

  // Buscar datos de pago del asociado
  const actualBankAccount = await AssociatePayments.findOne({ associateEmail: driverEmail });
  if (!actualBankAccount) {
    return 'No se encontró la cuenta del asociado';
  }
  const oldMonexClabe = actualBankAccount.oneCarNowClabe || actualBankAccount.monexClabe;
  // Obtener datos del asociado

  // Actualizar Clabe según el fondo
  if (fondo === 'i80-1') {
    // Crear una nueva Clabe i80 si no existe
    if (!actualBankAccount.i80Clabe) {
      const { firstName, lastName } = associatedData;
      const userData = {
        alias: firstName + ' ' + lastName, // Reemplazar por un valor si es necesario
        currency_code: wire4Data.bankAccount.currency_code,
        email: [associatedData.email],
        name: `${firstName} ${lastName}`,
      };
      const { clabe } = await createWire4I80BankAccount(userData);
      actualBankAccount.i80Clabe = clabe;
      await actualBankAccount.save();
    }

    await AssociatePayments.findOneAndUpdate(
      { associateEmail: driverEmail },
      { monexClabe: actualBankAccount.i80Clabe, oldMonexClabe },
      { new: true }
    );
    await updatePaymentProject(associatedData.clientId, actualBankAccount.i80Clabe, oldMonexClabe);
    await StockVehicle.findOneAndUpdate(
      { carNumber: contract },
      { $set: { transferredTo: 'i80-1' } },
      { new: true }
    );
  } else {
    if (!actualBankAccount.oneCarNowClabe) {
      const { firstName, lastName } = associatedData;
      const userData = {
        alias: firstName + ' ' + lastName, // Reemplazar por un valor si es necesario
        currency_code: wire4Data.bankAccount.currency_code,
        email: [associatedData.email],
        name: `${firstName} ${lastName}`,
      };
      const { clabe } = await createWire4BankAccount(userData);
      actualBankAccount.oneCarNowClabe = clabe;
      await actualBankAccount.save();
    }
    await AssociatePayments.findOneAndUpdate(
      { associateEmail: driverEmail },
      { monexClabe: actualBankAccount.oneCarNowClabe },
      { new: true }
    );
    await updatePaymentProject(associatedData.clientId, actualBankAccount.oneCarNowClabe, oldMonexClabe);
    await StockVehicle.findOneAndUpdate(
      { carNumber: contract },
      { $set: { transferredTo: null } },
      { new: true }
    );
    return 'Cuenta actualizada';
  }
  try {
    const { data } = await axios.get(`${PAYMENTS_API_URL}/payments?status=pending&clientId=${associateId}`, {
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    });
    if (data?.data[0]?.status === 'pending') {
      await axios.post(
        'https://api.hilos.io/api/channels/whatsapp/template/2ce47da8-ddfa-425c-9b70-db9645ac8f53/send',
        {
          phone: driverPhone,
          variables: [driverName, data.data[0].id],
        },
        {
          headers: {
            Authorization: `Token ${process.env.HILOS_API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );
    }
  } catch (error) {
    console.error(error);
  }
  return 'Cuenta actualizada';
}
