/* eslint-disable @typescript-eslint/no-use-before-define */
import { HILOS_API_KEY } from '../../constants';
import { HILOS_TEMPLATES, HILOS_URL, OCN_USER_EMAIL } from '../../constants/onboarding';
import { AdmissionRequestSerializer } from '../../clean/presentation/serializers';
import { createAdmissionRequest } from '../../clean/domain/usecases';
import { VehicleType } from '../../clean/domain/enums';
import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';
import axios from 'axios';
import { logger } from '@/clean/lib/logger';
import { IUserInstace, UserMongo } from '@/models/userSchema';

interface SendHilosParams {
  phone: string;
  personalData: any;
  type: 'newLead';
  hubspotId: string;
  source?: string;
  clientIpAddress?: string;
}

export const sendHilos = async ({
  phone,
  personalData,
  type,
  hubspotId,
  source,
  clientIpAddress,
}: SendHilosParams) => {
  const user = await UserMongo.findOne({ email: OCN_USER_EMAIL }).lean();

  const authUser = {
    userId: user?._id.toString() || '647f69776a98b82801ddcc45',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60,
    role: user?.role || 'superadmin',
    email: user?.email || '<EMAIL>',
  };

  try {
    const headers = {
      Authorization: `Token ${HILOS_API_KEY}`,
      'Content-Type': 'application/json',
    };

    const admissionRequest = await createAdmissionRequest(
      personalData,
      authUser,
      clientIpAddress,
      VehicleType.car,
      source
    );
    AdmissionRequestSerializer(admissionRequest);
    const requestId = admissionRequest.id;
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [requestId],
        phone,
      },
      { headers }
    );

    const conversationId = response.data.id.conversation;

    await AdmissionRequestMongo.findByIdAndUpdate(requestId, {
      hilos: response.data.id,
    });

    const updadeHubspot = await axios.patch(`${HILOS_URL}/${hubspotId}`, {
      properties: {
        request_id: requestId,
      },
    });

    await AdmissionRequestMongo.findByIdAndUpdate(requestId, {
      hubspot: updadeHubspot.data.id,
    });

    await fetch(`https://api.hilos.io/api/contact/${response.data.contact}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Token ${HILOS_API_KEY}`,
      },
      body: JSON.stringify({
        meta: {
          request_id: requestId,
        },
      }),
    });

    try {
      const agentUser = await UserMongo.findById(admissionRequest.agentId).select('_id email hilosUserId');

      if (agentUser) {
        if (!agentUser.hilosUserId) {
          await setHilosUserIdToUser({ user: agentUser }); // Updates the user and assigns to the same agentUser variable
        }

        if (agentUser.hilosUserId) {
          const updated = await updateHilosConversation({
            conversationId,
            hilosUserId: agentUser.hilosUserId,
          });
          if (updated) {
            // for migration purposes
            await AdmissionRequestMongo.updateOne({ _id: requestId }, { $set: { normalized: true } });
          }
        }
      }
    } catch (error) {
      console.error(error);
    }
    return { hilos: response.data, hubspot: updadeHubspot.data };
  } catch (error: any) {
    console.error(error);
    return error;
  }
};

type UpdateHilosConversationByConversationId = {
  hilosUserId: number;
  conversationId: string;
  contactId?: string;
};

type UpdateHilosConversationByContactId = {
  hilosUserId: number;
  conversationId?: string;
  contactId: string;
};

type UpdateHilosConversationProps =
  | UpdateHilosConversationByConversationId
  | UpdateHilosConversationByContactId;

export async function updateHilosConversation({
  conversationId,
  contactId,
  hilosUserId,
}: UpdateHilosConversationProps) {
  try {
    const headers = {
      Authorization: `Token ${HILOS_API_KEY}`,
      'Content-Type': 'application/json',
    };

    if (!conversationId) {
      const urlListConversations = `https://api.hilos.io/api/inbox/conversation?contact=${contactId}&page_size=2`;

      const { data: conversations } = await axios.get(urlListConversations, { headers });

      conversationId = conversations.results[0]?.id;
    }

    const response = await axios.patch(
      `https://api.hilos.io/api/inbox/conversation/${conversationId}/update`,
      {
        assigned: [{ id: hilosUserId }],
      },
      { headers }
    );
    console.log('[updateHilosConversation] response', response.data);
    return true;
  } catch (error: any) {
    console.error('error on [updateHilosConversation]', error);
    logger.error(
      `[updateHilosConversation] Error updating hilos conversation ${conversationId}`,
      error.toString()
    );
    return false;
  }
}

export async function setHilosUserIdToUser({ user }: { user: IUserInstace }) {
  if (!user.email) return user.toObject();

  try {
    // First try:
    const { data: hilosUsers } = await axios.get('https://api.hilos.io/api/account-member', {
      params: {
        email__iexact: user.email,
      },
      headers: {
        Authorization: `Token ${HILOS_API_KEY}`,
        'Content-Type': 'application/json',
      },
    });
    const userFoundInHilos = hilosUsers[0];
    console.log('userFoundInHilos', userFoundInHilos);
    if (userFoundInHilos) {
      user.hilosUserId = userFoundInHilos.id; // save it in object reference
      await user.save();
    }

    return user.toObject();
  } catch (error: any) {
    // second try:
    // add a "+1" before "@" in the email string
    // there are users in hilos with this format, example: <EMAIL> in admin panel and it's <EMAIL> in hilos
    let possibleEmailInHilos = user.email.split('@')[0] + '+1@' + user.email.split('@')[1];
    try {
      const { data: hilosUsers } = await axios.get('https://api.hilos.io/api/account-member', {
        params: {
          email__iexact: possibleEmailInHilos,
        },
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });
      const userFoundInHilos = hilosUsers[0];

      if (userFoundInHilos) {
        user.hilosUserId = userFoundInHilos.id; // save it in object reference
        await user.save();
      }

      return user.toObject();
    } catch (error1: any) {
      console.error('error on [setHilosUserIdToUser]: ', error);
      logger.error(
        `[setHilosUserIdToUser] Error setting hilosUserId to user ${user.email}`,
        error.toString()
      );
      return user.toObject();
    }
  }
}

export interface ISendHomeVisitAppointmentScheduleLink {
  phone: string;
  name: string;
  type: 'appointmentScheduler';
  requestId: string;
}

export const sendHomeVisitAppointmentScheduleLink = async ({
  phone,
  type,
  requestId,
}: ISendHomeVisitAppointmentScheduleLink) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  try {
    /*
      Scheduling link
    */
    // http://localhost:3001/6728bba05a6a1f025454d3af/schedule-home-visit
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [`${requestId}/schedule-home-visit`],
        phone: phone,
      },
      { headers }
    );
    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentScheduleLink] - Home visit appointment scheduling whatsapp message failed to send to clientId ${requestId} due to error: ${error}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentScheduledMessage {
  date: string;
  startTime: string;
  phone: string;
  meetingLink: string;
  requestId: string;
  type: 'homeVisitAppointmentScheduled';
}
export const sendHomeVisitAppointmentScheduledMessage = async ({
  date,
  startTime,
  phone,
  meetingLink,
  requestId,
  type,
}: ISendHomeVisitAppointmentScheduledMessage) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  try {
    /*
      ReScheduling link
    */
    // http://localhost:3001/6728bba05a6a1f025454d3af/schedule-home-visit?step=reschedule
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [date, startTime, meetingLink, `${requestId}/schedule-home-visit?step=reschedule`],
        phone: phone,
      },
      { headers }
    );

    logger.info(
      `[sendHomeVisitAppointmentScheduledMessage] Home visit appointment scheduled message sent to clientId ${requestId}, message id: ${response.data.id}`
    );

    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentScheduledMessage] - Home visit appointment scheduled messagee failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitApprovalOrRejectionMessage {
  requestId: string;
  phone: string;
  type: 'homeVisitApproval' | 'homeVisitRejection';
}
export const sendHomeVisitApprovalOrRejectionMessage = async ({
  requestId,
  phone,
  type,
}: ISendHomeVisitApprovalOrRejectionMessage) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };
  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: type === 'homeVisitApproval' ? [`?id=${requestId}`] : [],
        phone: phone,
      },
      { headers }
    );
    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitApprovalOrRejectionMessage] - error occurred while sending home visit ${type} message`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
  return null;
};

interface ISendHomeVisitAppointmentReminderMessageOneNightAgoCron {
  name: string;
  date: string;
  startTime: string;
  phone: string;
  meetingLink: string;
  requestId: string;
  type: 'homeVisitAppointmentReminderOneNightAgo';
}
export const sendHomeVisitAppointmentReminderMessageOneNightAgoCron = async ({
  phone,
  meetingLink,
  requestId,
  type,
  date,
  startTime,
}: ISendHomeVisitAppointmentReminderMessageOneNightAgoCron) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [date, startTime, meetingLink, `${requestId}/schedule-home-visit?step=reschedule`],
        phone: phone,
      },
      { headers }
    );

    logger.info(
      `[sendHomeVisitAppointmentReminderMessageOneNightAgoCron] - Home visit appointment reminder one night ago message sent to clientId ${requestId}, message id: ${response.data.id}`
    );

    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentReminderMessageOneNightAgoCron] - error occured while sending home visit appointment reminder one night ago message to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentReminderMessageAboutFiveMinutes {
  name: string;
  date: string;
  startTime: string;
  phone: string;
  meetingLink: string;
  requestId: string;
  type: 'homeVisitAppointmentReminderAboutFiveMinutes';
}
export const sendHomeVisitAppointmentReminderMessageAboutFiveMinutes = async ({
  phone,
  meetingLink,
  requestId,
  type,
  startTime,
}: ISendHomeVisitAppointmentReminderMessageAboutFiveMinutes) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [startTime, meetingLink, `${requestId}/schedule-home-visit?step=reschedule`],
        phone: phone,
      },
      { headers }
    );
    logger.info(
      `[sendHomeVisitAppointmentReminderMessageAboutFiveMinutes] - Home visit appointment reminder about five minutes message sent to clientId ${requestId} with message id: ${response.data.id}`
    );
    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentReminderMessageAboutFiveMinutes] - error occured while sending home visit appointment reminder about five minutes message to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentFinishMessage {
  name: string;
  phone: string;
  type: 'homeVisitAppointmentFinish';
}
export const sendHomeVisitAppointmentFinishMessage = async ({
  phone,
  type,
}: ISendHomeVisitAppointmentFinishMessage) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [],
        phone: phone,
      },
      { headers }
    );
    logger.info(
      `[sendHomeVisitAppointmentFinishMessage] - Home visit appointment finish message sent with id ${response.data.id}.`
    );
    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentFinishMessage] - Home visit appointment finish message failed to send.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentNoShowMessage {
  name: string;
  phone: string;
  requestId: string;
  type: 'homeVisitAppointmentNoShow';
}
export const sendHomeVisitAppointmentNoShowMessage = async ({
  phone,
  type,
  requestId,
}: ISendHomeVisitAppointmentNoShowMessage) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };
  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [`${requestId}/schedule-home-visit?step=reschedule`],
        phone: phone,
      },
      { headers }
    );

    logger.info(
      `[sendHomeVisitAppointmentNoShowMessage] - Home visit appointment NoShow WhatsApp message sent with id ${response.data.id} to clientId ${requestId}`
    );

    return response.data;
  } catch (error) {
    logger.error(
      `[sendHomeVisitAppointmentNoShowMessage] - Home visit appointment NoShow message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

interface ISendHomeVisitAppointmentApologyMessage {
  name: string;
  phone: string;
  requestId: string;
  type: 'homeVisitAppointmentApology';
}
export const sendHomeVisitAppointmentApologyMessage = async ({
  phone,
  type,
  requestId,
}: ISendHomeVisitAppointmentApologyMessage) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [`${requestId}/schedule-home-visit?step=reschedule`],
        phone: phone,
      },
      { headers }
    );

    logger.info(
      `[sendHomeVisitAppointmentApologyMessage] - Home visit appointment Apology WhatsApp message sent with id ${response.data.id} to clientId ${requestId}`
    );

    return response.data;
  } catch (error) {
    logger.error(
      `[sendHomeVisitAppointmentApologyMessage] - Home visit appointment Apology message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

interface ISendHomeVisitAppointmentCancelMessage {
  phone: string;
  requestId: string;
  type: 'homeVisitAppointmentCancel';
}
export const sendHomeVisitAppointmentCancelMessage = async ({
  phone,
  type,
  requestId,
}: ISendHomeVisitAppointmentCancelMessage) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [`${requestId}/schedule-home-visit?step=reschedule`],
        phone: phone,
      },
      { headers }
    );

    logger.info(
      `[sendHomeVisitAppointmentCancelMessage] - Home visit appointment Cancel WhatsApp message sent with id ${response.data.id} to clientId ${requestId}`
    );

    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentCancelMessage] - Home visit appointment Cancel WhatsApp failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendLocationGatheringMessageToUser extends Omit<ISendHomeVisitAppointmentApologyMessage, 'type'> {
  type: 'locationGatheringMessage';
}
export const sendLocationGatheringMessageToUser = async ({
  name,
  phone,
  requestId,
  type,
}: ISendLocationGatheringMessageToUser) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };
  const url = `${requestId}/user-info-gathering`;
  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [name, url],
        phone: phone,
      },
      { headers }
    );

    logger.info(
      `[sendLocationGatheringMessageToUser] - Location gathering WhatsApp message sent with id ${response.data.id} to clientId ${requestId}`
    );

    return response.data;
  } catch (error) {
    logger.error(
      `[sendLocationGatheringMessageToUser] - Location gathering whatsApp message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
  return null;
};

interface ISendOTPVerification {
  phone: string;
  otp: string;
  type: 'otpVerification';
}

export const sendOTPVerification = async ({ phone, otp, type }: ISendOTPVerification) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };
  logger.info(`[sendOTPVerification] - ${JSON.stringify({ phone, otp, type })}`);
  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [otp],
        phone: phone,
      },
      { headers }
    );
    logger.info(`[sendOTPVerification] - ${response.data}`);
    return response.data;
  } catch (error) {
    logger.error(`[sendOTPVerification] - ${JSON.stringify(error)}`);
    throw error;
  }
};

interface ISendOnboardingSupport {
  phone: string;
  type: 'onboardingSupport';
}

export const sendOnboardingSupport = async ({ phone, type }: ISendOnboardingSupport) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };
  logger.info(`[sendOnboardingSupport] - ${JSON.stringify({ phone, type })}`);
  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [],
        phone: phone,
      },
      { headers }
    );
    logger.info(`[sendOnboardingSupport] - ${response.data}`);
    return response.data;
  } catch (error) {
    logger.error(`[sendOnboardingSupport] - ${JSON.stringify(error)}`);
    throw error;
  }
};

interface ISendOnboardingSupportEnd {
  phone: string;
  type: 'onboardingSupportEnd';
}

export const sendOnboardingSupportEnd = async ({ phone, type }: ISendOnboardingSupportEnd) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };
  logger.info(`[sendOnboardingSupportEnd] - ${JSON.stringify({ phone, type })}`);
  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [],
        phone: phone,
      },
      { headers }
    );
    logger.info(`[onboardingSupportEnd] - ${response.data}`);
    return response.data;
  } catch (error) {
    logger.error(`[onboardingSupportEnd] - ${JSON.stringify(error)}`);
    throw error;
  }
};

interface ISendHomeImageUploadMessageToUser extends Omit<ISendHomeVisitAppointmentApologyMessage, 'type'> {
  type: 'homeImageUploadMessage';
}
export const sendHomeImageUploadMessageToUser = async ({
  name,
  phone,
  requestId,
  type,
}: ISendHomeImageUploadMessageToUser) => {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };
  const url = `${requestId}/home-image-upload`;
  try {
    const response = await axios.post(
      `${HILOS_URL}/${HILOS_TEMPLATES[type].templateId}/send`,
      {
        variables: [name, url],
        phone: phone,
      },
      { headers }
    );

    logger.info(
      `[sendHomeImageUploadMessageToUser] - Home Image upload message sent with id ${response.data.id} to clientId ${requestId}`
    );

    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendHomeImageUploadMessageToUser] - Home Image upload message failed to send to clientId ${requestId}.`,
      {
        message: error?.message,
        stack: error?.stack,
      }
    );
  }
};
