import { logger } from '../../clean/lib/logger';
import { DocumentCategory } from '../../types&interfaces/vehicleDocuments';

export enum DocumentProcessingErrorType {
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  DUPLICATE_DOCUMENT = 'DUPLICATE_DOCUMENT',
  INVALID_DOCUMENT = 'INVALID_DOCUMENT',
  VALIDITY_EXPIRED = 'VALIDITY_EXPIRED',
  INVALID_VALIDITY_DATE = 'INVALID_VALIDITY_DATE',
  PROCESSING_ERROR = 'PROCESSING_ERROR',
  VEHICLE_NOT_FOUND = 'VEHICLE_NOT_FOUND',
  CIRCULATION_SERVICE_DOWN = 'CIRCULATION_SERVICE_DOWN',
  VIN_NOT_FOUND = 'VIN_NOT_FOUND',
}

export const DATE_FORMAT_REGEX = /^\d{4}-\d{2}-\d{2}$/;

export class DocumentProcessingError extends Error {
  public type: DocumentProcessingErrorType;

  public status: string;

  public technicalMessage?: string;

  constructor(opts: {
    type: DocumentProcessingErrorType;
    message: string;
    status?: string;
    technicalMessage?: string;
  }) {
    super(opts.message);
    this.name = 'DocumentProcessingError';
    this.type = opts.type;
    this.status =
      opts.status ||
      (opts.type === 'FILE_TOO_LARGE'
        ? 'skipped'
        : opts.type === 'DUPLICATE_DOCUMENT'
          ? 'duplicate'
          : 'error');
    this.technicalMessage = opts.technicalMessage;
  }
}

export class DocumentValidators {
  /**
   * Normalizes date string to YYYY-MM-DD format
   * Accepts various formats: YYYY/MM/DD, YYYY.MM.DD, DD/MM/YYYY, etc.
   */
  static normalizeDateString(dateStr: string): string | null {
    if (!dateStr || typeof dateStr !== 'string') {
      return null;
    }

    const trimmed = dateStr.trim();

    // If already in correct format, return as-is
    if (DATE_FORMAT_REGEX.test(trimmed)) {
      return trimmed;
    }

    // Handle various separators and normalize to dashes
    let normalized = trimmed.replace(/[\/\._]/g, '-');

    // Remove multiple consecutive dashes
    normalized = normalized.replace(/-+/g, '-');

    // Remove leading/trailing dashes
    normalized = normalized.replace(/^-+|-+$/g, '');

    // Split by dashes
    const parts = normalized.split('-');

    if (parts.length === 3) {
      const [part1, part2, part3] = parts;

      // Check if first part is a 4-digit year (YYYY-MM-DD format)
      if (part1.length === 4 && /^\d{4}$/.test(part1)) {
        const year = part1;
        const month = part2.padStart(2, '0');
        const day = part3.padStart(2, '0');

        if (this.isValidDateParts(year, month, day)) {
          return `${year}-${month}-${day}`;
        }
      }

      // Check if last part is a 4-digit year (DD-MM-YYYY format)
      if (part3.length === 4 && /^\d{4}$/.test(part3)) {
        const year = part3;
        const month = part2.padStart(2, '0');
        const day = part1.padStart(2, '0');

        if (this.isValidDateParts(year, month, day)) {
          return `${year}-${month}-${day}`;
        }
      }

      // Check if middle part is a 4-digit year (MM-YYYY-DD format - less common)
      if (part2.length === 4 && /^\d{4}$/.test(part2)) {
        const year = part2;
        const month = part1.padStart(2, '0');
        const day = part3.padStart(2, '0');

        if (this.isValidDateParts(year, month, day)) {
          return `${year}-${month}-${day}`;
        }
      }
    }

    // Try to parse as a date and extract components
    const date = new Date(trimmed);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }

    return null;
  }

  /**
   * Validates that year, month, day are valid date components
   */
  private static isValidDateParts(year: string, month: string, day: string): boolean {
    const yearNum = parseInt(year, 10);
    const monthNum = parseInt(month, 10);
    const dayNum = parseInt(day, 10);

    if (yearNum < 1900 || yearNum > 2100) return false;
    if (monthNum < 1 || monthNum > 12) return false;
    if (dayNum < 1 || dayNum > 31) return false;

    // Create date to validate it exists (handles leap years, month lengths, etc.)
    const date = new Date(yearNum, monthNum - 1, dayNum);
    return date.getFullYear() === yearNum && date.getMonth() === monthNum - 1 && date.getDate() === dayNum;
  }

  /**
   * Normalizes all date fields in OCR data
   */
  static normalizeAllDateFields(ocrData: any, documentCategory: DocumentCategory): any {
    const dateFieldsMap: Record<DocumentCategory, string[]> = {
      [DocumentCategory.INSURANCE_POLICY]: ['issueDate', 'validity'],
      [DocumentCategory.TENENCIA]: ['validity', 'paymentDate'],
      [DocumentCategory.CIRCULATION_CARD_FRONT]: ['issueDate', 'validity'],
      [DocumentCategory.CIRCULATION_CARD_BACK]: [],
      [DocumentCategory.PLATES_ALTA_PLACAS]: [],
      [DocumentCategory.PLATES_FRONT]: [],
      [DocumentCategory.PLATES_BACK]: [],
      [DocumentCategory.FACTURE]: ['billDate'],
    };

    const dateFields = dateFieldsMap[documentCategory] || [];
    const normalizedData = { ...ocrData };

    for (const field of dateFields) {
      if (normalizedData[field]) {
        const originalValue = normalizedData[field];
        const normalizedValue = this.normalizeDateString(originalValue);

        if (normalizedValue) {
          normalizedData[field] = normalizedValue;
          if (originalValue !== normalizedValue) {
            logger.info(
              `[DocumentValidators] Normalized date field '${field}': '${originalValue}' → '${normalizedValue}'`
            );
          }
        } else {
          logger.warn(`[DocumentValidators] Could not normalize date field '${field}': '${originalValue}'`);
        }
      }
    }

    return normalizedData;
  }

  /**
   * Validates and normalizes validity date with special handling for circulation card front
   */
  static processValidityDate(ocrResult: any, documentCategory: DocumentCategory, isRequired: boolean): void {
    if (!ocrResult.validity) {
      return; // No validity field present
    }

    // Normalize the date first
    const normalizedDate = this.normalizeDateString(ocrResult.validity);

    if (normalizedDate) {
      ocrResult.validity = normalizedDate;
    }

    const validityDate = new Date(ocrResult.validity);

    // Special handling for circulation card front: if validity format is invalid, just remove it
    if (isNaN(validityDate.getTime())) {
      if (documentCategory === DocumentCategory.CIRCULATION_CARD_FRONT) {
        logger.warn(
          `[VehicleDocService] Invalid validity date format for circulation card front: ${ocrResult.validity}. Removing validity field.`
        );
        ocrResult.validity = undefined; // Remove invalid validity field
        return;
      } else if (isRequired) {
        const errorMessage = `Formato de fecha de validez inválido: ${ocrResult.validity}`;
        logger.error(
          `[VehicleDocService] ${errorMessage}, OCR Result: ${JSON.stringify(ocrResult)}, Document Category: ${documentCategory}`
        );
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.INVALID_VALIDITY_DATE,
          message: errorMessage,
          status: 'skipped',
        });
      }
    } else {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (validityDate <= today && isRequired) {
        const errorMessage = `Fecha de validez expirada: ${ocrResult.validity}`;
        logger.error(
          `[VehicleDocService] ${errorMessage}, OCR Result: ${JSON.stringify(
            ocrResult
          )}, Document Category: ${documentCategory}`
        );
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.VALIDITY_EXPIRED,
          message: errorMessage,
          status: 'skipped',
        });
      }
    }
  }

  /**
   * Non-throwing version of processValidityDate
   */
  static processValidityDateNonThrowing(
    ocrResult: any,
    documentCategory: DocumentCategory,
    isRequired: boolean
  ): string[] {
    const validationErrors: string[] = [];

    if (!ocrResult.validity) {
      return validationErrors; // No validity field present
    }

    // Normalize the date first
    const normalizedDate = this.normalizeDateString(ocrResult.validity);

    if (normalizedDate) {
      ocrResult.validity = normalizedDate;
    }

    const validityDate = new Date(ocrResult.validity);

    // Special handling for circulation card front: if validity format is invalid, just remove it
    if (isNaN(validityDate.getTime())) {
      if (documentCategory === DocumentCategory.CIRCULATION_CARD_FRONT) {
        logger.warn(
          `[DocumentValidators] Invalid validity date format for circulation card front: ${ocrResult.validity}. Removing validity field (validation bypassed).`
        );
        ocrResult.validity = undefined; // Remove invalid validity field
        return validationErrors;
      } else if (isRequired) {
        validationErrors.push(`Formato de fecha de validez inválido: ${ocrResult.validity}`);
        logger.error(
          `[VehicleDocService] Invalid validity date format: ${ocrResult.validity}, OCR Result: ${JSON.stringify(
            ocrResult
          )}, Document Category: ${documentCategory}`
        );
      }
    } else {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (validityDate <= today && isRequired) {
        validationErrors.push(`Fecha de validez expirada: ${ocrResult.validity}`);
        logger.error(
          `[VehicleDocService] Validity date expired: ${ocrResult.validity}, OCR Result: ${JSON.stringify(
            ocrResult
          )}, Document Category: ${documentCategory}`
        );
      }
    }

    return validationErrors;
  }

  /**
   * Sanitizes validation errors to prevent exposing internal implementation details
   */
  static sanitizeValidationErrorsForClient(errors: string[]): string[] {
    const baseErrorMessage =
      'Documento identificado como inválido, asegúrese de que es válido y contiene todos los datos requeridos de forma clara';
    return errors.map((error) => {
      const lowerError = error.toLowerCase();

      // Check for marker-related errors
      if (
        lowerError.includes('marcadores') ||
        lowerError.includes('marker') ||
        lowerError.includes('autenticidad') ||
        lowerError.includes('authenticity')
      ) {
        return baseErrorMessage;
      }

      // Check for field-related errors
      if (
        lowerError.includes('campos de datos') ||
        lowerError.includes('faltantes') ||
        lowerError.includes('placeholder') ||
        lowerError.includes('problemas con campos')
      ) {
        return baseErrorMessage;
      }

      // Check for LLM-specific errors
      if (
        lowerError.includes('llm') ||
        lowerError.includes('not returned by llm') ||
        lowerError.includes('reported as not found by llm')
      ) {
        return baseErrorMessage;
      }

      // Return original error if it doesn't contain internal implementation details
      return error;
    });
  }
}
