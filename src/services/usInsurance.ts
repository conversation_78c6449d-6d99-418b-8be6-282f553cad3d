interface CustomRange {
  min: number;
  max?: number; // Opcional para rangos abiertos como "75+"
  factor: number;
}

interface InsuranceFactors {
  basePrice: number; // Precio base por día
  age: CustomRange[];
  creditScore: CustomRange[];
  violations: { minor: number[]; major: number[]; missing: number };
  territory: Record<string, number>;
  msrp: CustomRange[];
}

// Datos extraídos del CSV (hardcodeados por ahora)
export const factors: InsuranceFactors = {
  basePrice: 11.56,
  age: [
    { min: 25, max: 25, factor: 1.34 },
    { min: 26, max: 26, factor: 1.34 },
    { min: 27, max: 27, factor: 1.29 },
    { min: 28, max: 28, factor: 1.24 },
    { min: 29, max: 29, factor: 1.21 },
    { min: 30, max: 30, factor: 1.15 },
    { min: 31, max: 35, factor: 1.06 },
    { min: 36, max: 39, factor: 1.0 },
    { min: 40, max: 49, factor: 0.97 },
    { min: 50, max: 59, factor: 0.9 },
    { min: 60, max: 69, factor: 0.87 },
    { min: 70, max: 74, factor: 0.93 },
    { min: 75, factor: 1.1 },
  ],
  creditScore: [
    { min: 640, max: 649, factor: 1.32 },
    { min: 650, max: 674, factor: 1.22 },
    { min: 675, max: 699, factor: 1.11 },
    { min: 700, max: 724, factor: 1.0 },
    { min: 725, max: 749, factor: 0.9 },
    { min: 750, max: 774, factor: 0.75 },
    { min: 775, max: 799, factor: 0.68 },
    { min: 800, max: 824, factor: 0.61 },
    { min: 825, max: 849, factor: 0.54 },
    { min: 850, factor: 0.51 },
  ],
  violations: {
    minor: [1, 1.25, 1.43, 1.69, 2.5],
    major: [1, 1.28, 1.59, 1.83, 2.15],
    missing: 2.5,
  },
  territory: {
    Miami: 1,
    Orlando: 0.88,
    Jacksonville: 0.88,
    Tampa: 0.91,
    'Rest of FL': 0.72,
    'Atlanta Metro': 0.74,
    Atlanta: 0.92,
    'GA Rest of State': 0.65,
    'GA Suburbs and Other Urban': 0.66,
    'TX Inner Metro Areas': 0.63,
    'TX Inner Suburbs': 0.58,
    'TX Major Urban': 0.73,
    'TX Minor Urban': 0.68,
    'TX Outer Metro Areas': 0.59,
    'TX Outer Suburbs': 0.55,
    'TX Rest of State': 0.6,
  },
  msrp: [
    { min: 0, max: 24999, factor: 0.97 },
    { min: 25000, max: 29999, factor: 0.97 },
    { min: 30000, max: 34999, factor: 0.98 },
    { min: 35000, max: 39999, factor: 0.98 },
    { min: 40000, max: 44999, factor: 0.99 },
    { min: 45000, max: 49999, factor: 1.0 },
    { min: 50000, max: 54999, factor: 1.01 },
    { min: 55000, max: 59999, factor: 1.04 },
    { min: 60000, max: 64999, factor: 1.05 },
    { min: 65000, max: 69999, factor: 1.07 },
    { min: 70000, max: 79999, factor: 1.11 },
    { min: 80000, max: 89999, factor: 1.13 },
    { min: 90000, max: 100000, factor: 1.18 },
  ],
};

export class InsuranceService {
  private factors: InsuranceFactors;

  constructor(insuranceFactors: InsuranceFactors) {
    this.factors = insuranceFactors;
  }

  // Buscar factor en un rango
  private getFactor(value: number, ranges: CustomRange[]): number {
    const range = ranges.find((r) => value >= r.min && (!r.max || value <= r.max));
    return range ? range.factor : 1; // Factor por defecto si no hay coincidencia
  }

  // Calcular prima diaria
  calculateDailyPremium({
    age,
    creditScore,
    minorViolations,
    majorViolations,
    territory,
    msrp,
    hasMissingMVR = false,
  }: {
    age: number;
    creditScore: number;
    minorViolations: number;
    majorViolations: number;
    territory: string;
    msrp: number;
    hasMissingMVR?: boolean;
  }): number {
    const ageFactor = this.getFactor(age, this.factors.age);
    const creditScoreFactor = this.getFactor(creditScore, this.factors.creditScore);
    const msrpFactor = this.getFactor(msrp, this.factors.msrp);
    const territoryFactor = this.factors.territory[territory] || 1;
    const violationFactor = hasMissingMVR
      ? this.factors.violations.missing
      : this.factors.violations.minor[minorViolations] * this.factors.violations.major[majorViolations];

    return (
      this.factors.basePrice * ageFactor * creditScoreFactor * msrpFactor * territoryFactor * violationFactor
    );
  }

  calculateMonthlyPremium(args: {
    age: number;
    creditScore: number;
    minorViolations: number;
    majorViolations: number;
    territory: string;
    msrp: number;
    hasMissingMVR?: boolean;
  }): number {
    return this.calculateDailyPremium(args) * 30;
  }
}

// Uso
const service = new InsuranceService(factors);
const dailyPremium = service.calculateDailyPremium({
  age: 30,
  creditScore: 750,
  minorViolations: 1,
  majorViolations: 0,
  territory: 'Miami',
  msrp: 45000,
});
const monthlyPremium = service.calculateMonthlyPremium({
  age: 30,
  creditScore: 750,
  minorViolations: 1,
  majorViolations: 0,
  territory: 'Miami',
  msrp: 45000,
});
console.log(`Prima diaria: $${dailyPremium.toFixed(2)}`);
console.log(`Prima mensual: $${monthlyPremium.toFixed(2)}`);
