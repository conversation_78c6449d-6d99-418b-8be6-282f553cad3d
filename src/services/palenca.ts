import { logger } from '@/clean/lib/logger';
import { PalencaProfile, Profile } from '@/models/palencaProfileSchema';
import axios from 'axios';
import { PALENCA_HOST, PALENCA_V2_API_KEY } from '@/constants';
import { PalencaEarningsV2 } from '@/models/palencaEarningsSchema';
import { AdmissionRequestMongo } from '@/models/admissionRequestSchema';
import { Types } from 'mongoose';
import { MetricMongo } from '@/models/metricsSchema';
import { EarningMongo } from '@/models/earningSchema';
import { calculateEarningsAnalysis } from './socialScoring/calculateEarningsAnalysis';

/**
 * Define types for webhook action
 */
type WebhookAction = 'login.created' | 'login.incomplete' | 'login.error' | 'login.success';

/**
 * Define the shape of the webhook event data
 */
interface WebhookData {
  account_id: string;
  external_id: string | null;
  platform: string;
  user_id: string;
  status_details: string;
  webhook_action: WebhookAction;
}

/**
 * Full webhook payload received from Palenca
 */
export interface WebhookPayload {
  data: WebhookData;
  webhook_url: string;
}

const fetchPalencaEarnings = async (accountId: string, platform: string) => {
  try {
    logger.info(`[Palenca Fetch Earning] --> Processing account: ${accountId}`);
    // Step 2: Calculate date range based on platform
    const today = new Date();
    let daysAgo = 100;
    const startDate = new Date();
    startDate.setDate(today.getDate() - daysAgo);

    const formatToISODate = (date: Date): string => date.toISOString().split('T')[0];

    const body = {
      start_date: formatToISODate(startDate),
      end_date: formatToISODate(today),
      options: {
        items_per_page: 100,
        page: 1,
      },
    };
    logger.info(`[Palenca Fetch Earning] --> Earning API request body: ${JSON.stringify(body)}`);
    // Step 3: Fetch earnings data
    const earningsResponse = await axios.post(
      `${PALENCA_HOST}/v1/accounts/${accountId}/earnings/search`,
      body,
      {
        headers: {
          'X-API-Key': `${PALENCA_V2_API_KEY}`,
        },
      }
    );
    logger.info(
      `[Palenca Fetch Earning] --> Earning API response body: ${JSON.stringify(earningsResponse.data)}`
    );
    if (!earningsResponse.data.success) {
      logger.info(
        `[Palenca Fetch Earning] --> Failed to fetch earnings: ${JSON.stringify(earningsResponse.data.error)}`
      );
      throw new Error(`Failed to fetch earnings: ${JSON.stringify(earningsResponse.data.error)}`);
    }

    const earningsData = earningsResponse.data.data;

    // Convert earning_date strings to Date objects
    const formattedEarnings = earningsData.earnings.map((entry: any) => ({
      ...entry,
      earning_date: new Date(entry.earning_date),
    }));

    // Step 4: Save earnings data
    await PalencaEarningsV2.findOneAndUpdate(
      { account_id: earningsData.account_id, platform: platform },
      {
        account_id: earningsData.account_id,
        earnings: formattedEarnings,
        pagination: earningsData.pagination,
      },
      { upsert: true, new: true, runValidators: true }
    );

    logger.info(`[Palenca Fetch Earning] Successfully stored earnings for account: ${accountId}`);
  } catch (error: any) {
    logger.error(
      `[Palenca Fetch Earning] Error fetching earnings for account ${accountId} error:  ${error.message}`,
      {
        stack: error.stack,
      }
    );
    throw Error(`Error fetching earnings:  ${error.message}`);
  }
};

/**
 * Handles login.success event (can be extended later)
 */
export const fetchPalencaData = async (accountId: string, isCron: boolean, user_id: string | null) => {
  logger.info(`[Palenca Fetch Data] --> Processing account: ${accountId}`);

  try {
    const url = `${PALENCA_HOST}/v1/accounts/${accountId}/profile`;
    logger.info(`[Palenca Fetch Data] --> url: ${url}`);
    logger.info(`[Palenca Fetch Data] --> PALENCA_V2_API_KEY: ${PALENCA_V2_API_KEY}`);
    // Make API call to fetch profile data
    const response = await axios.get(url, {
      headers: {
        'X-API-Key': `${PALENCA_V2_API_KEY}`,
      },
    });

    const apiData = response.data;

    if (!apiData.success) {
      logger.info(`[Palenca Fetch Data] --> Failed to fetch profile: ${JSON.stringify(apiData.error)}`);
      throw new Error(`Failed to fetch profile: ${JSON.stringify(apiData.error)}`);
    }

    const accountUrl = `${PALENCA_HOST}/v1/accounts/${accountId}`;
    logger.info(`[Palenca Fetch Data] --> accountUrl: ${accountUrl}`);
    const accResponse = await axios.get(accountUrl, {
      headers: {
        'X-API-Key': `${PALENCA_V2_API_KEY}`,
      },
    });

    const acc = accResponse.data;
    if (!acc.success) {
      logger.info(`[Palenca Fetch Data] --> Failed to fetch account: ${JSON.stringify(acc.error)}`);
      throw new Error(`Failed to fetch account: ${JSON.stringify(acc.error)}`);
    }
    const accData = acc.data;
    const data = apiData.data;
    logger.info(`[Palenca Fetch Data] --> Profile API response data: ${JSON.stringify(data)}`);
    // Convert birthday to Date object (if exists)
    const birthday = data.profile.birthday ? new Date(data.profile.birthday) : null;

    // Build the document to upsert
    const profileData = {
      account_id: data.account_id,
      external_id: data.external_id,
      platform: accData.platform,
      first_name: data.profile.first_name || null,
      last_name: data.profile.last_name || null,
      email: data.profile.email || null,
      birthday,
      phone: data.profile.phone || null,
      address: data.profile.address || null,
      city_name: data.profile.city_name || null,
      picture_url: data.profile.picture_url || null,
      ids_info: data.ids_info || [],
      bank_info: data.bank_info || null,
      vehicle_info: {
        type: data.vehicle_info?.type || null,
        brand: data.vehicle_info?.brand || null,
        model: data.vehicle_info?.model || null,
        year: data.vehicle_info?.year || null,
        license_plate: data.vehicle_info?.license_plate || null,
        vin: data.vehicle_info?.vin || null,
      },
      metrics_info: {
        acceptance_rate: data.metrics_info?.acceptance_rate ?? null,
        cancellation_rate: data.metrics_info?.cancellation_rate ?? null,
        rating: data.metrics_info?.rating ?? null,
        lifetime_trips: data.metrics_info?.lifetime_trips ?? null,
        time_since_first_trip: data.metrics_info?.time_since_first_trip || null,
        level_name: data.metrics_info?.level_name || null,
        debt_pending: data.metrics_info?.debt_pending ?? null,
        debt_paid: data.metrics_info?.debt_paid ?? null,
        activation_status: data.metrics_info?.activation_status || null,
      },
      is_cron_processed: isCron,
      user_id: user_id,
    };

    // Upsert into MongoDB
    await PalencaProfile.findOneAndUpdate(
      { account_id: data.account_id, platform: accData.platform },
      profileData,
      {
        upsert: true,
        new: true,
        runValidators: true,
      }
    );

    logger.info(`[Palenca Fetch Data] Successfully stored profile for account: ${data.account_id}`);

    await fetchPalencaEarnings(accountId, accData.platform);
  } catch (error: any) {
    logger.error(`[Palenca Fetch Data] Error fetching or saving profile:  ${error.message}`, {
      stack: error.stack,
    });
    throw Error(`Error fetching or saving profile/earnings:  ${error.message}`);
  }
};

const addNewPalancaAccount = async (requestId: string, platform: string, accountId: string) => {
  const newAccount = {
    _id: new Types.ObjectId(),
    accountId: accountId,
    platform,
    earnings: {
      status: 'pending',
      _id: new Types.ObjectId(),
    },
    metrics: {
      status: 'pending',
      _id: new Types.ObjectId(),
    },
    status: 'pending',
  };
  await AdmissionRequestMongo.updateOne(
    { _id: requestId },
    {
      $addToSet: {
        'palenca.accounts': newAccount, // This will add `newAccount` only if it's not already in the array.
      },
    }
  );
  logger.info({
    message: 'New Palanca account created',
    requestId: requestId,
    platform: platform,
    method: 'addNewPalancaAccount',
  });
};

const addPlatformToAdmissionRequest = async (requestId: string, platform: string, accountId: string) => {
  const admissionRequest = await AdmissionRequestMongo.findOne({
    _id: new Types.ObjectId(requestId),
  });
  if (!admissionRequest) {
    logger.error({
      status: 400,
      message: 'Admission Request record not found',
      requestId: requestId,
      platform: platform,
      method: 'addPlatformToAdmissionRequest',
    });
    throw new Error('AdmissionRequest record not found');
  }
  const existingAccount = admissionRequest?.palenca?.accounts?.filter((account) => {
    return account?.platform === platform;
  });
  if (!existingAccount || existingAccount.length === 0) {
    //add new account
    addNewPalancaAccount(requestId, platform, accountId);
  }
};

const parseTimeUsingApp = (value: string) => {
  if (!value || typeof value !== 'string') return 0;

  const lowerValue = value.toLowerCase();

  // Handle "New" or invalid input
  if (lowerValue === 'new') {
    return 0;
  }

  // Improved regex: allows for symbols like +, ~, etc., and ignores extra text before/after
  const regex = /([-+]?\d+(\.\d+)?)\s*([+~]?)\s*(year|years|month|months|day|days)/i;
  const match = lowerValue.match(regex);

  if (!match) return 0;

  const numericValue = parseFloat(match[1]);
  const unit = match[4].toLowerCase();

  switch (unit) {
    case 'year':
    case 'years':
      return numericValue;
    case 'month':
    case 'months':
      return numericValue / 12; // Convert months to years
    case 'day':
    case 'days':
      return numericValue / 365; // Convert days to years
    default:
      return 0; // Shouldn't reach here
  }
};

const addProfileDataToMetrix = async (profileData: Profile, requestId: string, platform: string) => {
  const existingMetric = await MetricMongo.findOne({ requestId: requestId, platform: platform });
  if (existingMetric) {
    existingMetric.acceptanceRate =
      profileData?.metrics_info?.acceptance_rate !== null ? profileData?.metrics_info?.acceptance_rate : 0;
    existingMetric.rating =
      profileData?.metrics_info?.rating !== null ? profileData?.metrics_info?.rating : 0;
    existingMetric.lifetimeTrips =
      profileData?.metrics_info?.lifetime_trips !== null ? profileData?.metrics_info?.lifetime_trips : 0;
    existingMetric.timeSinceFirstTrip =
      profileData?.metrics_info?.time_since_first_trip !== null
        ? parseTimeUsingApp(profileData?.metrics_info?.time_since_first_trip)
        : 0;
    existingMetric.cancellationRate =
      profileData?.metrics_info?.cancellation_rate !== null
        ? profileData?.metrics_info?.cancellation_rate
        : 0;
    existingMetric.save();

    logger.info({
      message: 'Existing Uber Metric Saved',
      requestId: requestId,
      platform: platform,
      method: 'saveUberDriverMetrics',
      earning: JSON.stringify(existingMetric),
    });
  } else {
    const metric = await MetricMongo.create({
      platform,
      requestId,
      acceptanceRate:
        profileData?.metrics_info?.acceptance_rate !== null ? profileData?.metrics_info?.acceptance_rate : 0,
      cancellationRate:
        profileData?.metrics_info?.cancellation_rate !== null
          ? profileData?.metrics_info?.cancellation_rate
          : 0,
      rating: profileData?.metrics_info?.rating !== null ? profileData?.metrics_info?.rating : 0,
      lifetimeTrips:
        profileData?.metrics_info?.lifetime_trips !== null ? profileData?.metrics_info?.lifetime_trips : 0,
      timeSinceFirstTrip:
        profileData?.metrics_info?.time_since_first_trip !== null
          ? parseTimeUsingApp(profileData?.metrics_info?.time_since_first_trip)
          : 0,
      activationStatus: 'documents_analysis',
    });
    logger.info({
      message: `New ${platform} Metric Saved`,
      requestId: requestId,
      platform: platform,
      method: 'saveUberDriverMetrics',
      earning: JSON.stringify(metric),
    });
  }
};

export const processPalencaProfile = async (accountId: string): Promise<boolean> => {
  const profileData = await PalencaProfile.findOne({ account_id: accountId });
  //update profile metrics data using palenca data
  if (profileData && profileData.external_id) {
    // add platform to addmission request
    await addPlatformToAdmissionRequest(profileData.external_id, profileData.platform, accountId);
    // add profile metrics to MetricMongo
    await addProfileDataToMetrix(profileData, profileData.external_id, profileData.platform);
  } else {
    logger.error(`processPalencaProfile] - Profile Data not found: ${accountId}`);
    return false;
  }

  const earningData = await PalencaEarningsV2.findOne({ account_id: accountId });
  logger.info(`[processPalencaProfile] --> earningData: ${JSON.stringify(earningData)}}`);
  //update earning metrics data using palenca data
  if (earningData) {
    //add earning data to Earning Mongo
    if (earningData.earnings && earningData.earnings.length > 0) {
      const weeklyMap = new Map();

      for (const entry of earningData.earnings) {
        const date = new Date(entry.earning_date);
        // Get Monday of that week
        const dayOfWeek = date.getUTCDay(); // 0 = Sunday, 1 = Monday
        const diffToMonday = (dayOfWeek + 6) % 7; // days to subtract
        const monday = new Date(date);
        monday.setUTCDate(monday.getUTCDate() - diffToMonday);

        const mondayKey = monday.toISOString().split('T')[0]; // e.g., "2025-04-14"

        if (!weeklyMap.has(mondayKey)) {
          weeklyMap.set(mondayKey, {
            amount: 0,
            countTrips: 0,
            earningDate: monday,
          });
        }

        const weekEntry = weeklyMap.get(mondayKey);
        weekEntry.amount += entry.amount;
        weekEntry.countTrips += entry.count_trips;
      }
      logger.info(`[processPalencaProfile] --> weeklyMap: ${JSON.stringify(weeklyMap)}}`);
      // Save each weekly entry to EarningMongo
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
      for (const [, week] of weeklyMap) {
        const earning = new EarningMongo({
          amount: week.amount,
          currency: 'mxn',
          earningDate: week.earningDate.toISOString(),
          countTrips: week.countTrips,
          requestId: profileData.external_id,
          cashAmount: 0,
          platform: profileData.platform,
        });
        await earning.save();
      }
      logger.info(`[processPalencaProfile] --> Earning data saved successfully`);
    }
  }
  return true;
};

/**
 * Cron Job to process Unprocessed Palenca Profiles
 */
export const processUnprocessedPalencaProfiles = async () => {
  logger.info(`[PalencaService] --> Process started`);
  const profile = await PalencaProfile.findOne({
    $or: [{ is_cron_processed: false }, { is_cron_processed: { $exists: false } }],
  });

  if (profile) {
    try {
      const accountUrl = `${PALENCA_HOST}/v1/accounts/${profile.account_id}/user`;
      logger.info(`[PalencaService] --> Fetch Account's User Url: ${accountUrl}`);

      const accResponse = await axios.get(accountUrl, {
        headers: {
          'X-API-Key': PALENCA_V2_API_KEY,
        },
      });

      const acc = accResponse.data;

      if (!acc.success || !acc.data?.user_id) {
        logger.warn(
          `[PalencaService] --> Failed to fetch ${profile.account_id} Account's User: ${JSON.stringify(acc.error)}`
        );
        return;
      }
      const userId = acc.data.user_id;
      const userUrl = `${PALENCA_HOST}/v1/users/${userId}/accounts`;
      logger.info(`[PalencaService] --> Fetch User's Accounts Url: ${userUrl}`);

      const userResponse = await axios.get(userUrl, {
        headers: {
          'X-API-Key': PALENCA_V2_API_KEY,
        },
      });

      const user = userResponse.data;

      if (!user.success || !user.data?.accounts) {
        logger.warn(
          `[PalencaService] --> Failed to fetch ${userId} User's Account: ${JSON.stringify(user.error)}`
        );
        return;
      }

      logger.info(
        `[PalencaService] --> ${user.data.accounts.length} Accounts to process for user: ${userId}`
      );

      for (const account of user.data.accounts) {
        let isProcessed = false;
        const profileData = await PalencaProfile.findOne({ account_id: account.account_id });
        if (!profileData) {
          logger.warn(`[PalencaService] - Profile Data not found: ${account.account_id}`);
          await fetchPalencaData(account.account_id, true, userId);
          isProcessed = true;
        } else {
          const earningsData = await PalencaEarningsV2.findOne({ account_id: profileData.account_id });
          if (!earningsData || earningsData.earnings.length === 0) {
            logger.warn(`[PalencaService] - Earnings Data not found: ${profileData.account_id}`);
            try {
              await fetchPalencaEarnings(profileData.account_id, profileData.platform);
              isProcessed = true;
            } catch {}
          } else {
            logger.info(
              `[PalencaService] - Data is good no action required for account: ${account.account_id}`
            );
          }
        }
        if (isProcessed) {
          const success = await processPalencaProfile(account.account_id);
          if (success) {
            logger.info(
              `[PalencaService] - processPalencaProfile updated successfully for: ${account.account_id}`
            );
            const result = await calculateEarningsAnalysis(profile.external_id!);
            if (result.earningsAnalysis.status) {
              logger.info(
                `[PalencaService] - calculateEarningsAnalysis updated successfully for request id: ${profile.external_id}`
              );
            } else {
              logger.warn(
                `[PalencaService] - calculateEarningsAnalysis failled for request id: ${profile.external_id}`
              );
            }
          } else {
            logger.warn(`[PalencaService] - processPalencaProfile failled for: ${account.account_id}`);
          }
        }
      }

      // Mark profile as processed
      try {
        profile.is_cron_processed = true;
        profile.user_id = userId;
        await profile.save();
      } catch (saveErr) {
        logger.error(`[PalencaService] --> Failed to save profile ${profile.account_id}: ${saveErr}`);
      }
    } catch (err: any) {
      logger.error(
        `[PalencaService] --> Error processing profile ${profile.account_id}: ${err?.stack || err?.toString()}`
      );
      try {
        profile.is_cron_processed = true;
        profile.user_id = null;
        await profile.save();
      } catch (saveErr) {
        logger.error(`[PalencaService] --> Failed to save profile ${profile.account_id}: ${saveErr}`);
      }
    }
  }
  logger.info(`[PalencaService] --> Process ended`);
};
