import { Request } from 'express';
import StockVehicle from '../models/StockVehicleSchema';
import User from '../models/userSchema';
import { replaceDocWithUrl } from './getPropertyWithUrls';

/**
 * Returns an object with properties passed by second parameter
 * @param formData  object
 * @param properties array of strings
 */

export function parsedProperties(formData: Request['body'], properties: string[]): Record<string, any> {
  const datosAnalizados: Record<string, any> = {};

  properties.forEach((property) => {
    const valor = formData[property];
    datosAnalizados[property] = valor ? JSON.parse(valor) : null;
  });

  return datosAnalizados;
}

export function parseBody(formData: Request['body']): Record<string, any> {
  const datosAnalizados: Record<string, any> = {};

  for (const property in formData) {
    if (Object.prototype.hasOwnProperty.call(formData, property)) {
      const valor = formData[property];
      datosAnalizados[property] = valor ? JSON.parse(valor) : null;
    }
  }

  return datosAnalizados;
}

const stock = new StockVehicle();

type HistoryArray = (typeof stock)['updateHistory'];

/**
 * Returns an array of sorted history data with user information
 * @param {HistoryArray} historyArray should be typeof HistoryArray
 */

export async function getUpdateHistoryWithUserInfo(historyArray: HistoryArray) {
  const updateHistory = historyArray;

  // Get unique user IDs to avoid duplicate queries
  const uniqueUserIds = [...new Set(updateHistory.map((item) => item.userId))];

  // Fetch all users with image field included in single query
  const users = await User.find({ _id: { $in: uniqueUserIds } }, { name: 1, image: 1 }).lean();

  // Create a Map for O(1) user lookups instead of O(n) array.find()
  const usersMap = new Map(users.map((user) => [user._id.toString(), user]));

  // Get all unique image IDs for batch processing
  const imageIds = users.filter((user) => user.image).map((user) => user?.image?.toString());

  // Process all images in parallel instead of sequentially
  const imagePromises = imageIds.map((imageId) => replaceDocWithUrl(imageId));
  const imageUrls = await Promise.all(imagePromises);

  // Create image URL map for quick lookups
  const imageUrlMap = new Map(imageIds.map((imageId, index) => [imageId, imageUrls[index]]));

  // Process history items efficiently
  const updateHistoryWithUserInfo = updateHistory.map((item) => {
    const user = usersMap.get(item.userId.toString());

    if (!user) {
      return {
        ...item,
        user: { name: 'Usuario eliminado', image: { docId: '', url: '', originalName: '' } },
      };
    }

    let userImage;
    if (user.image) {
      const imageUrl = imageUrlMap.get(user.image.toString());
      userImage = imageUrl || { docId: '', url: '', originalName: '' };
    } else {
      userImage = { docId: '', url: '', originalName: '' };
    }

    return {
      ...item,
      user: {
        ...user,
        image: userImage,
      },
    };
  });

  // Sort by time (most recent first)
  const sortedUpdateHistory = updateHistoryWithUserInfo.sort((a, b) => {
    const timeA = new Date(a.time ?? '').getTime();
    const timeB = new Date(b.time ?? '').getTime();
    return timeB - timeA;
  });

  return sortedUpdateHistory;
}
