import { logger } from '@/clean/lib/logger';
import { CountriesEnum, OTP_EXPIRY } from '@/constants';
import OTP from '@/models/otpSchema';
import { sendOTPEmailToCustomer } from '@/modules/platform_connections/emailFunc';
import { sendOTPVerification } from '@/services/onboarding/sendHilos';
import { requestCodeVerification, sendVerificationCode } from './twilio/twilio';

interface IOTPRequest {
  id: string;
  email?: string;
  phone: string;
  country: CountriesEnum;
}
interface IOTPVerificationRequest extends IOTPRequest {
  code: string;
}

// Generate new OTP
export const generateAndSendOTP = async ({ phone, email, id, country }: IOTPRequest): Promise<boolean> => {
  let isOTPGeneratedAndSend = false;
  try {
    // TODO: Check for country if US then send Email and OTP via Twilio
    if (country === CountriesEnum['United States']) {
      // Send OTP via SMS
      isOTPGeneratedAndSend = await sendVerificationCode({ toPhoneNumber: phone });
      // TODO: Send OTP via Email
    } else {
      // Generate OTP
      const otpData = await OTP.createNewOTP({
        associateId: id,
        email,
        phone,
        expiryMinutes: OTP_EXPIRY,
      });
      if (email) {
        // Send verification code on email
        await sendOTPEmailToCustomer({
          otp: otpData.otp,
          customerEmail: email,
          subject: 'Tu código de verificación',
        });
      }
      // Send verification code on phone number
      await sendOTPVerification({
        phone: phone,
        otp: otpData.otp,
        type: 'otpVerification',
      });
      isOTPGeneratedAndSend = true;
    }
  } catch (error) {
    logger.error(`[generateAndSendOTP] Unable to send verification code to ${phone}`, error);
  }
  return isOTPGeneratedAndSend;
};

export const verifyCode = async ({
  phone,
  email,
  id,
  country,
  code,
}: IOTPVerificationRequest): Promise<boolean> => {
  let isVerified = false;
  try {
    if (country === CountriesEnum['United States']) {
      isVerified = await requestCodeVerification({ toPhoneNumber: phone, code: code });
    } else {
      // Verify OTP
      const otpDoc = await OTP.verifyOTP({
        associateId: id,
        email,
        phone,
        otp: code,
      });
      if (otpDoc) {
        // Mark OTP as used
        await otpDoc.markAsUsed();
        logger.info(`[verifyOTP] OTP ${otpDoc} marked as used`);
        isVerified = true;
      }
    }
  } catch (error) {
    logger.error(`[verifyOTP] OTP verification failed`, error);
  }
  return isVerified;
};
