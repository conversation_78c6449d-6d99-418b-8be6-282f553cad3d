import { Types } from 'mongoose';
import VehicleInspection, {
  VehicleInspectionStatus,
  IVehicleInspection,
} from '../models/vehicleInspectionSchema';
import { logger } from '../clean/lib/logger';
import { getUrlFromArray } from '../aws/s3';

/**
 * Get vehicle inspection by vehicle ID with signed URLs for photos
 * @param vehicleId - The vehicle ID to get inspection for
 * @returns Promise<IVehicleInspection | null> - The inspection with signed URLs or null if not found
 */
export const getVehicleInspectionService = async (vehicleId: string): Promise<IVehicleInspection | null> => {
  try {
    if (!Types.ObjectId.isValid(vehicleId)) {
      throw new Error('Invalid vehicle ID format.');
    }

    // Get the most recent inspection for this vehicle
    const inspection = await VehicleInspection.findOne({ vehicleId: new Types.ObjectId(vehicleId) }).sort({
      createdAt: -1,
    });

    if (!inspection) {
      return null;
    }

    // Get signed URLs for all photo arrays
    const [
      frontViewUrls,
      rightSideUrls,
      rearViewUrls,
      leftSideUrls,
      interiorPhotosWithUrls,
      odometerPhotosWithUrls,
      enginePhotosWithUrls,
      toolsPhotosWithUrls,
      chargerPhotosWithUrls,
      spareTirePhotosWithUrls,
      batteryPhotosWithUrls,
      issuePhotosWithUrls,
    ] = await Promise.all([
      getUrlFromArray(inspection.exterior360Photos?.frontView || []),
      getUrlFromArray(inspection.exterior360Photos?.rightSide || []),
      getUrlFromArray(inspection.exterior360Photos?.rearView || []),
      getUrlFromArray(inspection.exterior360Photos?.leftSide || []),
      getUrlFromArray(inspection.interiorPhotos || []),
      getUrlFromArray(inspection.odometerPhotos || []),
      getUrlFromArray(inspection.enginePhotos || []),
      getUrlFromArray(inspection.toolsPhotos || []),
      getUrlFromArray(inspection.chargerPhotos || []),
      getUrlFromArray(inspection.spareTirePhotos || []),
      getUrlFromArray(inspection.batteryPhotos || []),
      getUrlFromArray(inspection.issuePhotos || []),
    ]);

    // Reconstruct exterior360Photos object
    const exterior360PhotosWithUrls = {
      frontView: frontViewUrls,
      rightSide: rightSideUrls,
      rearView: rearViewUrls,
      leftSide: leftSideUrls,
    };

    // Update the inspection object with signed URLs
    inspection.exterior360Photos = exterior360PhotosWithUrls;
    inspection.interiorPhotos = interiorPhotosWithUrls;
    inspection.odometerPhotos = odometerPhotosWithUrls;
    inspection.enginePhotos = enginePhotosWithUrls;
    inspection.toolsPhotos = toolsPhotosWithUrls;
    inspection.chargerPhotos = chargerPhotosWithUrls;
    inspection.spareTirePhotos = spareTirePhotosWithUrls;
    inspection.batteryPhotos = batteryPhotosWithUrls;
    inspection.issuePhotos = issuePhotosWithUrls;

    logger.info(`[VehicleInspectionService] Retrieved inspection for vehicle: ${vehicleId}`);

    return inspection;
  } catch (error) {
    logger.error(`[getVehicleInspectionService] Error: ${error instanceof Error ? error.message : error}`);
    throw error;
  }
};

/**
 * Get all vehicle inspections by vehicle ID with signed URLs for photos (sorted by creation date)
 * @param vehicleId - The vehicle ID to get all inspections for
 * @returns Promise<IVehicleInspection[]> - Array of inspections with signed URLs
 */
export const getAllVehicleInspectionsService = async (vehicleId: string): Promise<IVehicleInspection[]> => {
  try {
    if (!Types.ObjectId.isValid(vehicleId)) {
      throw new Error('Invalid vehicle ID format.');
    }

    const inspections = await VehicleInspection.find({ vehicleId: new Types.ObjectId(vehicleId) }).sort({
      createdAt: -1,
    });

    if (!inspections || inspections.length === 0) {
      return [];
    }

    // Get signed URLs for all inspections
    const inspectionsWithUrls = await Promise.all(
      inspections.map(async (inspection) => {
        const [
          frontViewUrls,
          rightSideUrls,
          rearViewUrls,
          leftSideUrls,
          interiorPhotosWithUrls,
          odometerPhotosWithUrls,
          enginePhotosWithUrls,
          toolsPhotosWithUrls,
          chargerPhotosWithUrls,
          spareTirePhotosWithUrls,
          batteryPhotosWithUrls,
          issuePhotosWithUrls,
        ] = await Promise.all([
          getUrlFromArray(inspection.exterior360Photos?.frontView || []),
          getUrlFromArray(inspection.exterior360Photos?.rightSide || []),
          getUrlFromArray(inspection.exterior360Photos?.rearView || []),
          getUrlFromArray(inspection.exterior360Photos?.leftSide || []),
          getUrlFromArray(inspection.interiorPhotos || []),
          getUrlFromArray(inspection.odometerPhotos || []),
          getUrlFromArray(inspection.enginePhotos || []),
          getUrlFromArray(inspection.toolsPhotos || []),
          getUrlFromArray(inspection.chargerPhotos || []),
          getUrlFromArray(inspection.spareTirePhotos || []),
          getUrlFromArray(inspection.batteryPhotos || []),
          getUrlFromArray(inspection.issuePhotos || []),
        ]);

        // Reconstruct exterior360Photos object
        const exterior360PhotosWithUrls = {
          frontView: frontViewUrls,
          rightSide: rightSideUrls,
          rearView: rearViewUrls,
          leftSide: leftSideUrls,
        };

        // Update the inspection object with signed URLs
        inspection.exterior360Photos = exterior360PhotosWithUrls;
        inspection.interiorPhotos = interiorPhotosWithUrls;
        inspection.odometerPhotos = odometerPhotosWithUrls;
        inspection.enginePhotos = enginePhotosWithUrls;
        inspection.toolsPhotos = toolsPhotosWithUrls;
        inspection.chargerPhotos = chargerPhotosWithUrls;
        inspection.spareTirePhotos = spareTirePhotosWithUrls;
        inspection.batteryPhotos = batteryPhotosWithUrls;
        inspection.issuePhotos = issuePhotosWithUrls;

        return inspection;
      })
    );

    logger.info(
      `[VehicleInspectionService] Retrieved ${inspectionsWithUrls.length} inspections for vehicle: ${vehicleId}`
    );

    return inspectionsWithUrls;
  } catch (error) {
    logger.error(
      `[getAllVehicleInspectionsService] Error: ${error instanceof Error ? error.message : error}`
    );
    throw error;
  }
};

/**
 * Create vehicle inspection with images (always creates new record for history)
 * @param inspectionData - The inspection data to create
 * @returns Promise<IVehicleInspection> - The created inspection
 */
export const createOrUpdateInspectionService = async (inspectionData: {
  vehicleId: string;
  status: string;
  issueDescription?: string;
  exterior360Photos?: any;
  interiorPhotos?: string[];
  odometerPhotos?: string[];
  enginePhotos?: string[];
  toolsPhotos?: string[];
  chargerPhotos?: string[];
  spareTirePhotos?: string[];
  batteryPhotos?: string[];
  issuePhotos?: string[];
}): Promise<IVehicleInspection> => {
  try {
    const {
      vehicleId,
      status,
      issueDescription,
      exterior360Photos,
      interiorPhotos,
      odometerPhotos,
      enginePhotos,
      toolsPhotos,
      chargerPhotos,
      spareTirePhotos,
      batteryPhotos,
      issuePhotos,
    } = inspectionData;

    if (!Types.ObjectId.isValid(vehicleId)) {
      throw new Error('Invalid vehicle ID format.');
    }

    // Map PHYSICAL_PREPARATION to PASSED, RETURNED_TO_DEALER to FAILED
    const mappedStatus =
      status === 'PHYSICAL_PREPARATION' ? VehicleInspectionStatus.PASSED : VehicleInspectionStatus.FAILED;

    // Always create new inspection record for history
    const newInspection = new VehicleInspection({
      vehicleId: new Types.ObjectId(vehicleId),
      status: mappedStatus,
      issueDescription,
      exterior360Photos: exterior360Photos || { frontView: [], rightSide: [], rearView: [], leftSide: [] },
      interiorPhotos: interiorPhotos || [],
      odometerPhotos: odometerPhotos || [],
      enginePhotos: enginePhotos || [],
      toolsPhotos: toolsPhotos || [],
      chargerPhotos: chargerPhotos || [],
      spareTirePhotos: spareTirePhotos || [],
      batteryPhotos: batteryPhotos || [],
      issuePhotos: issuePhotos || [],
    });

    await newInspection.save();

    logger.info(`[VehicleInspectionService] Created new inspection for vehicle: ${vehicleId}`);

    return newInspection;
  } catch (error) {
    logger.error(
      `[createOrUpdateInspectionService] Error: ${error instanceof Error ? error.message : error}`
    );
    throw error;
  }
};
