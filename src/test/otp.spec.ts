import { generateAndSendOTP, verifyCode } from '@/services/otp';
import { CountriesEnum } from '@/constants';
import OTP from '@/models/otpSchema';
import { sendOTPEmailToCustomer } from '@/modules/platform_connections/emailFunc';
import { sendOTPVerification } from '@/services/onboarding/sendHilos';
import { sendVerificationCode } from '@/services/twilio/twilio';
import { requestCodeVerification } from '@/services/twilio/twilio';

jest.mock('@/models/otpSchema');
jest.mock('@/modules/platform_connections/emailFunc');
jest.mock('@/services/onboarding/sendHilos');
jest.mock('@/services/twilio/twilio');

describe('generateAndSendOTP', () => {
  const baseRequest = {
    id: '123',
    phone: '5551234567',
    email: '<EMAIL>',
    country: CountriesEnum.Mexico,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should send OTP via Twilio for United States', async () => {
    (sendVerificationCode as jest.Mock).mockResolvedValue(true);

    const result = await generateAndSendOTP({
      ...baseRequest,
      country: CountriesEnum['United States'],
    });

    expect(sendVerificationCode).toHaveBeenCalledWith({ toPhoneNumber: baseRequest.phone });
    expect(result).toBe(true);
  });

  it('should generate OTP, send email and SMS for non-US country', async () => {
    const otpData = { otp: '123456' };
    (OTP.createNewOTP as jest.Mock).mockResolvedValue(otpData);

    const result = await generateAndSendOTP(baseRequest);

    expect(OTP.createNewOTP).toHaveBeenCalledWith({
      associateId: baseRequest.id,
      email: baseRequest.email,
      phone: baseRequest.phone,
      expiryMinutes: expect.anything(), // Accepts any value
    });
    expect(sendOTPEmailToCustomer).toHaveBeenCalledWith({
      otp: otpData.otp,
      customerEmail: baseRequest.email,
      subject: 'Tu código de verificación',
    });
    expect(sendOTPVerification).toHaveBeenCalledWith({
      phone: baseRequest.phone,
      otp: otpData.otp,
      type: 'otpVerification',
    });
    expect(result).toBe(true);
  });

  it('should generate OTP and only send SMS if email is not provided', async () => {
    const otpData = { otp: '654321' };
    (OTP.createNewOTP as jest.Mock).mockResolvedValue(otpData);

    const result = await generateAndSendOTP({
      ...baseRequest,
      email: undefined,
    });

    expect(sendOTPEmailToCustomer).not.toHaveBeenCalled();
    expect(sendOTPVerification).toHaveBeenCalledWith({
      phone: baseRequest.phone,
      otp: otpData.otp,
      type: 'otpVerification',
    });
    expect(result).toBe(true);
  });

  it('should return false and log error if an exception occurs', async () => {
    (OTP.createNewOTP as jest.Mock).mockRejectedValue(new Error('fail'));

    const result = await generateAndSendOTP(baseRequest);

    expect(result).toBe(false);
  });
});

describe('verifyCode', () => {
  const baseRequest = {
    id: '123',
    phone: '5551234567',
    email: '<EMAIL>',
    country: CountriesEnum.Mexico,
    code: '123456',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should verify code via Twilio for United States', async () => {
    (requestCodeVerification as jest.Mock).mockResolvedValue(true);

    const result = await verifyCode({
      ...baseRequest,
      country: CountriesEnum['United States'],
    });

    expect(requestCodeVerification).toHaveBeenCalledWith({
      toPhoneNumber: baseRequest.phone,
      code: baseRequest.code,
    });
    expect(result).toBe(true);
  });

  it('should verify OTP and mark as used for non-US country', async () => {
    const markAsUsed = jest.fn().mockResolvedValue(undefined);
    (OTP.verifyOTP as jest.Mock).mockResolvedValue({ markAsUsed });

    const result = await verifyCode(baseRequest);

    expect(OTP.verifyOTP).toHaveBeenCalledWith({
      associateId: baseRequest.id,
      email: baseRequest.email,
      phone: baseRequest.phone,
      otp: baseRequest.code,
    });
    expect(markAsUsed).toHaveBeenCalled();
    expect(result).toBe(true);
  });

  it('should return false if OTP is not found', async () => {
    (OTP.verifyOTP as jest.Mock).mockResolvedValue(null);

    const result = await verifyCode(baseRequest);

    expect(result).toBe(false);
  });

  it('should return false and log error if exception occurs', async () => {
    (OTP.verifyOTP as jest.Mock).mockRejectedValue(new Error('fail'));

    const result = await verifyCode(baseRequest);

    expect(result).toBe(false);
  });
});
