import axios from 'axios';
import { SendAppointmentSchedulerLinkParams } from '@/services/onboarding/sendHilos';
import { HILOS_TEMPLATES, HILOS_URL } from '@/constants/onboarding';
import { HILOS_API_KEY } from '@/constants';

// Mocking axios
jest.mock('axios');

describe('sendAppointmentSchedulerLink', () => {
  const params: SendAppointmentSchedulerLinkParams = {
    phone: '1234567890',
    name: '<PERSON>',
    appointmentSchedulerPageLinkId: 'linkId123',
    type: 'appointmentScheduler',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should send appointment scheduler link successfully and return response data', async () => {
    // Mocked response from axios.post
    const mockResponse = {
      data: { success: true, message: 'Link sent successfully' },
    };

    // Mock axios.post to resolve successfully
    (axios.post as jest.Mock).mockResolvedValue(mockResponse);

    expect(axios.post).toHaveBeenCalledTimes(1);
    expect(axios.post).toHaveBeenCalledWith(
      `${HILOS_URL}/${HILOS_TEMPLATES[params.type].templateId}/send`,
      {
        variables: [
          'https://hilos.io/api/file/p/7f908be9-dd9f-4a70-a3d1-685499104854',
          params.name,
          params.appointmentSchedulerPageLinkId,
        ],
        phone: params.phone,
      },
      {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
  });

  it('should throw error when axios.post fails', async () => {
    // Mock axios.post to reject with an error
    const errorMessage = 'Network Error';
    (axios.post as jest.Mock).mockRejectedValue(new Error(errorMessage));
  });

  it('should use the correct templateId based on the type', async () => {
    // Mocked response from axios.post
    const mockResponse = { data: { success: true, message: 'Link sent successfully' } };
    (axios.post as jest.Mock).mockResolvedValue(mockResponse);

    // Assertions: Check that the correct templateId was used based on the type
    const expectedTemplateId = HILOS_TEMPLATES[params.type].templateId;
    expect(axios.post).toHaveBeenCalledWith(
      `${HILOS_URL}/${expectedTemplateId}/send`,
      expect.anything(),
      expect.anything()
    );
  });
});
