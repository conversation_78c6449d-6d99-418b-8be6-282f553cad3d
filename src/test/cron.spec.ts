import {
  homeVisitAppointmnetScheduleFirstReminder,
  homeVisitAppointmnetScheduleSecondReminder,
  homeVisitAppointmnetScheduleThirdReminder,
} from '../clean/domain/usecases';
import {
  getHomeVisitScheduleLinkSendTodayDates,
  getHomeVisitScheduleLinkSendYesterdayDates,
  getHomeVisitScheduleLinkSendFiveDaysAgo,
} from '../clean/data/mongoRepositories';
import { Appointment } from '../models/appointment';
import { logger } from '../clean/lib/logger';
import { sendHomeVisitAppointmentScheduleLinkReminder } from '@/services/onboarding/sendHilos';

jest.mock('@/services/onboarding/sendHilos', () => ({
  sendHomeVisitAppointmentScheduleLinkReminder: jest.fn(),
}));

jest.mock('../clean/data/mongoRepositories', () => ({
  getHomeVisitScheduleLinkSendTodayDates: jest.fn(),
  getHomeVisitScheduleLinkSendYesterdayDates: jest.fn(),
  getHomeVisitScheduleLinkSendFiveDaysAgo: jest.fn(),
}));

jest.mock('../models/appointment', () => ({
  Appointment: {
    findOne: jest.fn(),
  },
}));

jest.mock('../clean/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

describe('homeVisitAppointmnetScheduleFirstReminder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should send home visit appointment scheduling links to clients with no existing appointment', async () => {
    const todayHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
      {
        _id: '456',
        personalData: {
          firstName: 'Jane',
          phone: '0987654321',
        },
      },
    ];

    (getHomeVisitScheduleLinkSendTodayDates as jest.Mock).mockResolvedValue(todayHomeVisitRequests);

    (Appointment.findOne as jest.Mock).mockResolvedValue(null);

    await homeVisitAppointmnetScheduleFirstReminder();

    expect(logger.info).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleFirstReminder]: Sending home visit appointment scheduling links to ${todayHomeVisitRequests.length} clients with applications approved today.`
    );

    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledTimes(2);
    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledWith({
      phone: '1234567890',
      name: 'John',
      type: 'appointmentSchedulerReminder',
      requestId: '123',
    });
    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledWith({
      phone: '0987654321',
      name: 'Jane',
      type: 'appointmentSchedulerReminder',
      requestId: '456',
    });
  });

  it('should not send home visit appointment scheduling links if an appointment already exists', async () => {
    const todayHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
    ];

    (getHomeVisitScheduleLinkSendTodayDates as jest.Mock).mockResolvedValue(todayHomeVisitRequests);
    (Appointment.findOne as jest.Mock).mockResolvedValue({ _id: 'existingAppointmentId' });

    await homeVisitAppointmnetScheduleFirstReminder();

    expect(logger.info).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleFirstReminder] - Home visit appointment already exist for clientId 123`
    );

    expect(sendHomeVisitAppointmentScheduleLinkReminder).not.toHaveBeenCalled();
  });

  it('should log an error if sending the scheduling link fails', async () => {
    const todayHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
    ];

    const mockError = new Error('Failed to send link');

    (sendHomeVisitAppointmentScheduleLinkReminder as jest.Mock).mockRejectedValue(mockError);
    (getHomeVisitScheduleLinkSendTodayDates as jest.Mock).mockResolvedValue(todayHomeVisitRequests);

    (Appointment.findOne as jest.Mock).mockResolvedValue(null);

    (sendHomeVisitAppointmentScheduleLinkReminder as jest.Mock).mockRejectedValue(mockError);

    await homeVisitAppointmnetScheduleFirstReminder();

    expect(logger.error).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleFirstReminder] - Home visit appointment scheduling link failed to send to clientId 123,`,
      {
        message: mockError.message,
        stack: mockError.stack,
      }
    );
  });

  it("should log an error if fetching today's home visit requests fails", async () => {
    const mockError = new Error('Failed to fetch requests');

    (getHomeVisitScheduleLinkSendTodayDates as jest.Mock).mockRejectedValue(mockError);

    await homeVisitAppointmnetScheduleFirstReminder();

    expect(logger.error).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleFirstReminder] - Error occured while sending home visit appointment scheduling link to clients`,
      {
        message: mockError.message,
        stack: mockError.stack,
      }
    );
  });
});

describe('homeVisitAppointmnetScheduleSecondReminder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should send home visit appointment scheduling links to clients with no existing appointment', async () => {
    const yesterdayHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
      {
        _id: '456',
        personalData: {
          firstName: 'Jane',
          phone: '0987654321',
        },
      },
    ];

    (getHomeVisitScheduleLinkSendYesterdayDates as jest.Mock).mockResolvedValue(yesterdayHomeVisitRequests);
    (Appointment.findOne as jest.Mock).mockResolvedValue(null);

    await homeVisitAppointmnetScheduleSecondReminder();

    expect(logger.info).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleSecondReminder]: Sending home visit appointment scheduling links to ${yesterdayHomeVisitRequests.length} clients with applications approved yesterday.`
    );

    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledTimes(2);
    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledWith({
      phone: '1234567890',
      name: 'John',
      type: 'appointmentSchedulerReminder',
      requestId: '123',
    });
    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledWith({
      phone: '0987654321',
      name: 'Jane',
      type: 'appointmentSchedulerReminder',
      requestId: '456',
    });
  });

  it('should not send home visit appointment scheduling links if an appointment already exists', async () => {
    const yesterdayHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
    ];

    (getHomeVisitScheduleLinkSendYesterdayDates as jest.Mock).mockResolvedValue(yesterdayHomeVisitRequests);
    (Appointment.findOne as jest.Mock).mockResolvedValue({ _id: 'existingAppointmentId' });

    await homeVisitAppointmnetScheduleSecondReminder();

    expect(logger.info).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleSecondReminder] - Home visit appointment already exist for clientId 123`
    );

    expect(sendHomeVisitAppointmentScheduleLinkReminder).not.toHaveBeenCalled();
  });

  it('should log an error if sending the scheduling link fails', async () => {
    const yesterdayHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
    ];

    const mockError = new Error('Failed to send link');

    (getHomeVisitScheduleLinkSendYesterdayDates as jest.Mock).mockResolvedValue(yesterdayHomeVisitRequests);
    (Appointment.findOne as jest.Mock).mockResolvedValue(null);
    (sendHomeVisitAppointmentScheduleLinkReminder as jest.Mock).mockRejectedValue(mockError);

    await homeVisitAppointmnetScheduleSecondReminder();

    expect(logger.error).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleSecondReminder] - Home visit appointment scheduling link failed to send to clientId 123,`,
      {
        message: mockError.message,
        stack: mockError.stack,
      }
    );
  });

  it("should log an error if fetching yesterday's home visit requests fails", async () => {
    const mockError = new Error('Failed to fetch requests');

    (getHomeVisitScheduleLinkSendYesterdayDates as jest.Mock).mockRejectedValue(mockError);

    await homeVisitAppointmnetScheduleSecondReminder();

    expect(logger.error).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleSecondReminder] - Error occured while sending home visit appointment scheduling link to clients`,
      {
        message: mockError.message,
        stack: mockError.stack,
      }
    );
  });
});

describe('homeVisitAppointmnetScheduleThirdReminder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should send home visit appointment scheduling links to clients with no existing appointment', async () => {
    const fiveDaysAgoHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
      {
        _id: '456',
        personalData: {
          firstName: 'Jane',
          phone: '0987654321',
        },
      },
    ];

    (getHomeVisitScheduleLinkSendFiveDaysAgo as jest.Mock).mockResolvedValue(fiveDaysAgoHomeVisitRequests);
    (Appointment.findOne as jest.Mock).mockResolvedValue(null);

    await homeVisitAppointmnetScheduleThirdReminder();

    expect(logger.info).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleThirdReminder]: Sending home visit appointment scheduling links to ${fiveDaysAgoHomeVisitRequests.length} clients with applications approved five days ago.`
    );

    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledTimes(2);
    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledWith({
      phone: '1234567890',
      name: 'John',
      type: 'appointmentSchedulerReminder',
      requestId: '123',
    });
    expect(sendHomeVisitAppointmentScheduleLinkReminder).toHaveBeenCalledWith({
      phone: '0987654321',
      name: 'Jane',
      type: 'appointmentSchedulerReminder',
      requestId: '456',
    });
  });

  it('should not send home visit appointment scheduling links if an appointment already exists', async () => {
    const fiveDaysAgoHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
    ];

    (getHomeVisitScheduleLinkSendFiveDaysAgo as jest.Mock).mockResolvedValue(fiveDaysAgoHomeVisitRequests);
    (Appointment.findOne as jest.Mock).mockResolvedValue({ _id: 'existingAppointmentId' });

    await homeVisitAppointmnetScheduleThirdReminder();

    expect(logger.info).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleThirdReminder] - Home visit appointment already exist for clientId 123`
    );

    expect(sendHomeVisitAppointmentScheduleLinkReminder).not.toHaveBeenCalled();
  });

  it('should log an error if sending the scheduling link fails', async () => {
    const fiveDaysAgoHomeVisitRequests = [
      {
        _id: '123',
        personalData: {
          firstName: 'John',
          phone: '1234567890',
        },
      },
    ];

    const mockError = new Error('Failed to send link');

    (getHomeVisitScheduleLinkSendFiveDaysAgo as jest.Mock).mockResolvedValue(fiveDaysAgoHomeVisitRequests);

    (Appointment.findOne as jest.Mock).mockResolvedValue(null);

    (sendHomeVisitAppointmentScheduleLinkReminder as jest.Mock).mockRejectedValue(mockError);

    await homeVisitAppointmnetScheduleThirdReminder();

    expect(logger.error).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleThirdReminder] - Home visit appointment scheduling link failed to send to clientId 123,`,
      {
        message: mockError.message,
        stack: mockError.stack,
      }
    );
  });

  it('should log an error if fetching five days ago home visit requests fails', async () => {
    const mockError = new Error('Failed to fetch requests');

    (getHomeVisitScheduleLinkSendFiveDaysAgo as jest.Mock).mockRejectedValue(mockError);

    await homeVisitAppointmnetScheduleThirdReminder();

    expect(logger.error).toHaveBeenCalledWith(
      `[homeVisitAppointmnetScheduleThirdReminder] - Error occured while sending home visit appointment scheduling link to clients`,
      {
        message: mockError.message,
        stack: mockError.stack,
      }
    );
  });
});
