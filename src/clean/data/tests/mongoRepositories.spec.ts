// import {
//   AdmissionRequestMongo,
//   RequestDocumentsAnalysisMongoI,
//   RequestHomeVisitMongoI,
//   RequestPersonalDataMongoI,
// } from '../../../models/admissionRequestSchema';
// import {
//   repoInsertAdmissionRequest,
//   repoGetAdmissionRequestById,
//   repoAddPalencaAccountToAdmissionRequest,
//   repoUpdatePalencaAccountStatus,
//   repoUpdatePalencaAccountEarningsRetrievalStatus,
//   repoUpdatePalencaAccountMetricsRetrievalStatus,
//   repoSaveEarnings,
//   repoSaveMetric,
//   repoInsertDocument,
//   repoUpdateBatchDocumentStatus,
//   repoUpdateRequestPersonalData,
//   repoUpdateRequestDocuments,
//   repoCreateHomeVisit,
//   repoUpdateAdmissionRequestStatus,
//   repoRetrieveWeeklyEarnings,
//   repoSaveEarningsAnalysis,
//   repoGetPaginatedAdmissionRequests,
//   repoGetRequestDocumentAnalysisWithMedia,
//   repoUpdateRequestDocumentStatus,
//   repoRejectRequestDocument,
//   repoRequestUpdateRequestDocumentsAnalysisStatus,
//   repoGetHomeVisitWithMedia,
//   repoRetrievePalencaPlatformMetric,
//   repoRetrieveEvents,
//   repoSaveEvent,
//   repoSaveEvents,
//   repoSaveOrReplaceRiskAnalysisData,
//   repoSaveOrReplaceBatchRiskAnalysisData,
//   repoGetRiskAnalysisData,
//   repoGetScorecardConfig,
//   repoRetriveMetricsForAllPlatforms,
//   repoSaveRiskAnalysis,
// } from '../mongoRepositories';
// import {
//   EarningMongoFactory,
//   MetricMongoFactory,
//   DocumentMongoFactory,
//   RequestDocumentsDetailMongoFactory,
//   RequestHomeVisitMongoFactory,
//   RequestDocumentsAnalysisMongoFactory,
//   UserMongoFactory,
//   EventMongoFactory,
//   RiskAnalysisDataMongoFactory,
//   AdmissionRequestMongoFactory,
// } from './fakers';
// import {
//   RequestPersonalDataFactory,
//   PalencaAccountFactory,
//   DocumentFactory,
//   HomeVisitFactory,
//   EarningsAnalysisFactory,
//   ScorecardFactory,
//   AdmissionRequestFactory,
//   EarningFactory,
//   MetricFactory,
//   EventFactory,
//   PaginationSearchOptionsFactory,
// } from '../../domain/tests/fakers';
// import { connectTestDB, disconnectTestDB } from '../../../database/index';
// import {
//   RequestPersonalData,
//   RequestDocument,
//   RequestDocumentsAnalysis,
//   HomeVisit,
//   EarningsAnalysis,
//   AdmissionRequest,
//   WeeklyEarning,
//   Metric,
//   Event,
//   ScorecardConfig,
//   RiskAnalysis,
//   RiskAnalysisData,
// } from '../../domain/entities';
// import { EarningMongo } from '../../../models/earningSchema';
// import { MetricMongo } from '../../../models/metricsSchema';
// import DocumentMongo from '../../../models/documentSchema';
// import { EventMongo } from '../../../models/eventSchema';
// import { RiskAnalysisDataMongo, RiskAnalysisDataMongoI } from '../../../models/riskAnalysisDataSchema';
// import { Types } from 'mongoose';
// import {
//   AdmissionRequestStatus,
//   GigPlatform,
//   CurrencyCode,
//   MediaStatus,
//   RequestDocumentStatus,
//   RequestDocumentsAnalysisStatus,
//   PalencaAccountStatus,
//   AdmissionRequestDocumentType,
//   EntityType,
//   RiskAnalysisStatus,
//   ScorecardVariableName,
//   ScorecardVersion,
//   PalencaRetrievalStatus,
// } from '../../domain/enums';
// import { UserMongoI, UserMongo } from '../../../models/userSchema';

// jest.mock('../s3Repositories.ts', () => ({
//   repoGetMediaSignedUrl: jest.fn().mockResolvedValue('https://www.mocked-signed-url.com'),
// }));

// describe('Mongo Repositories', () => {
//   beforeAll(async () => connectTestDB(), 10000);

//   afterAll(async () => disconnectTestDB(), 10000);

//   beforeEach(async () => {
//     await AdmissionRequestMongo.deleteMany({});
//     await EarningMongo.deleteMany({});
//     await MetricMongo.deleteMany({});
//     await DocumentMongo.deleteMany({});
//     await EventMongo.deleteMany({});
//     await RiskAnalysisDataMongo.deleteMany({});
//   });

//   it('should insert admission request', async () => {
//     const fakeAdmissionRequest = AdmissionRequestFactory();

//     const admissionRequest = await repoInsertAdmissionRequest(fakeAdmissionRequest);

//     const foundAdmissionRequest = await AdmissionRequestMongo.findById(admissionRequest.id);

//     expect(admissionRequest).not.toBeNull();
//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);
//     expect(foundAdmissionRequest).not.toBeNull();
//     expect(foundAdmissionRequest?.id).toBe(admissionRequest.id);
//   });

//   it('should get admission request by id', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();
//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);
//     expect(admissionRequest?.id).toBe(saved._id.toString());
//   });

//   it('should add palenca account to admission request', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakePalencaAccount = PalencaAccountFactory();

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);
//     const admissionRequest = await repoAddPalencaAccountToAdmissionRequest(saved._id, fakePalencaAccount);

//     const foundAdmissionRequest = await AdmissionRequestMongo.findById(saved._id);

//     expect(admissionRequest).not.toBeNull();
//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);
//     expect(foundAdmissionRequest).not.toBeNull();
//     expect(saved.palenca.accounts).toHaveLength(2);
//     expect(admissionRequest?.palenca.accounts).toHaveLength(3);
//     expect(foundAdmissionRequest?.palenca.accounts).toHaveLength(3);
//   });

//   it('should update palenca account status', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);
//     const firstSavedPalencaAccount = saved.palenca.accounts[0];

//     const admissionRequest = await repoUpdatePalencaAccountStatus(
//       saved._id,
//       firstSavedPalencaAccount.accountId,
//       firstSavedPalencaAccount.platform as GigPlatform,
//       PalencaAccountStatus.success
//     );

//     const foundAdmissionRequest = await AdmissionRequestMongo.findById(saved._id);

//     const updatedAccount = foundAdmissionRequest?.palenca.accounts.find(
//       (account) => account.accountId === firstSavedPalencaAccount.accountId
//     );

//     expect(admissionRequest).not.toBeNull();
//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);
//     expect(foundAdmissionRequest).not.toBeNull();
//     expect(updatedAccount?.status).toBe(PalencaAccountStatus.success);
//   });

//   it('should update palenca account earnings retrieval status', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);
//     const firstSavedPalencaAccount = saved.palenca.accounts[0];

//     const admissionRequest = await repoUpdatePalencaAccountEarningsRetrievalStatus(
//       saved._id,
//       firstSavedPalencaAccount.accountId,
//       firstSavedPalencaAccount.platform as GigPlatform,
//       PalencaRetrievalStatus.success
//     );

//     const foundAdmissionRequest = await AdmissionRequestMongo.findById(saved._id);

//     const updatedAccount = foundAdmissionRequest?.palenca.accounts.find(
//       (account) => account.accountId === firstSavedPalencaAccount.accountId
//     );

//     expect(admissionRequest).not.toBeNull();
//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);
//     expect(foundAdmissionRequest).not.toBeNull();
//     expect(updatedAccount?.earnings.status).toBe(PalencaRetrievalStatus.success);
//   });

//   it('should update palenca account metrics retrieval status', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);
//     const firstSavedPalencaAccount = saved.palenca.accounts[0];

//     const admissionRequest = await repoUpdatePalencaAccountMetricsRetrievalStatus(
//       saved._id,
//       firstSavedPalencaAccount.accountId,
//       firstSavedPalencaAccount.platform as GigPlatform,
//       PalencaRetrievalStatus.success
//     );

//     const foundAdmissionRequest = await AdmissionRequestMongo.findById(saved._id);

//     const updatedAccount = foundAdmissionRequest?.palenca.accounts.find(
//       (account) => account.accountId === firstSavedPalencaAccount.accountId
//     );

//     expect(admissionRequest).not.toBeNull();
//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);
//     expect(foundAdmissionRequest).not.toBeNull();
//     expect(updatedAccount?.metrics.status).toBe(PalencaRetrievalStatus.success);
//   });

//   it('should save earnings', async () => {
//     const fakeEarning = EarningFactory();

//     await repoSaveEarnings([fakeEarning]);

//     const foundedEarnings = await EarningMongo.find({
//       requestId: fakeEarning.requestId,
//       platform: fakeEarning.platform,
//     });

//     expect(foundedEarnings).not.toBeNull();
//     expect(foundedEarnings).toHaveLength(1);
//     expect(foundedEarnings[0].requestId === fakeEarning.requestId);
//     expect(foundedEarnings[0].platform === fakeEarning.platform);
//   });

//   it('should save metric', async () => {
//     const fakeMetric = MetricFactory();

//     await repoSaveMetric(fakeMetric);

//     const foundedMetric = await MetricMongo.findOne({
//       requestId: fakeMetric.requestId,
//       platform: fakeMetric.platform,
//     });

//     expect(foundedMetric).not.toBeNull();
//     expect(foundedMetric?.requestId.toString()).toBe(fakeMetric.requestId);
//   });

//   it('should insert document', async () => {
//     const fakeDocument = DocumentFactory();

//     const inserted = await repoInsertDocument(fakeDocument);

//     const founded = await DocumentMongo.findById(inserted?.id);

//     expect(founded).not.toBeNull();

//     expect(founded?.id).toBe(inserted?.id);
//   });

//   it('should update batch document status', async () => {
//     const fakeDocumentOne = DocumentMongoFactory({
//       status: MediaStatus.pending as string,
//     });

//     const fakeDocumentTwo = DocumentMongoFactory({
//       status: MediaStatus.pending as string,
//     });

//     const savedOne = await DocumentMongo.create(fakeDocumentOne);
//     const savedTwo = await DocumentMongo.create(fakeDocumentTwo);

//     const updated = await repoUpdateBatchDocumentStatus(
//       [savedOne._id.toString(), savedTwo._id.toString()],
//       MediaStatus.active
//     );

//     const founded = await DocumentMongo.find({
//       _id: { $in: [savedOne._id, savedTwo._id] },
//     });
//     expect(updated).not.toBeNull();
//     expect(updated).toHaveLength(2);
//     expect(founded[0].status).toBe(MediaStatus.active);
//     expect(founded[1].status).toBe(MediaStatus.active);
//   });

//   it('should update request personal data', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakeRequestPersonalData = RequestPersonalDataFactory();

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     const updated = await repoUpdateRequestPersonalData(saved._id, fakeRequestPersonalData);

//     expect(updated).not.toBeNull();
//     expect(updated).toBeInstanceOf(AdmissionRequest);
//     expect(updated?.id).toBe(saved._id.toString());
//     expect(updated?.personalData).not.toBeNull();
//     expect(updated?.personalData).toBeInstanceOf(RequestPersonalData);
//     expect(updated?.personalData?.birthdate).toBe(fakeRequestPersonalData.birthdate);
//     expect(updated?.personalData?.taxId).toBe(fakeRequestPersonalData.taxId);
//     expect(updated?.personalData?.nationalId).toBe(fakeRequestPersonalData.nationalId);
//     expect(updated?.personalData?.postalCode).toBe(fakeRequestPersonalData.postalCode);
//     expect(updated?.personalData?.city).toBe(fakeRequestPersonalData.city);
//     expect(updated?.personalData?.state).toBe(fakeRequestPersonalData.state);
//     expect(updated?.personalData?.neighborhood).toBe(fakeRequestPersonalData.neighborhood);
//     expect(updated?.personalData?.street).toBe(fakeRequestPersonalData.street);
//     expect(updated?.personalData?.streetNumber).toBe(fakeRequestPersonalData.streetNumber);
//     expect(updated?.personalData?.department).toBe(fakeRequestPersonalData.department);
//   });

//   it('should update request documents', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const documents = fakeAdmissionRequest.documentsAnalysis?.documents;
//     const firstDocument = documents?.[0]!;
//     const secondDocument = documents?.[1]!;
//     firstDocument.type = AdmissionRequestDocumentType.bank_statement_month_1;
//     firstDocument.status = RequestDocumentStatus.pending_review;
//     secondDocument.type = AdmissionRequestDocumentType.bank_statement_month_2;
//     secondDocument.status = RequestDocumentStatus.pending_review;

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     const createdFirstDocument = admissionRequest?.documentsAnalysis?.documents?.[0];
//     const createdSecondDocument = admissionRequest?.documentsAnalysis?.documents?.[1];

//     createdFirstDocument.status = RequestDocumentStatus.approved;
//     createdSecondDocument.status = RequestDocumentStatus.approved;

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     const updated = await repoUpdateRequestDocuments(saved._id, [
//       createdFirstDocument,
//       createdSecondDocument,
//     ]);

//     expect(updated).not.toBeNull();
//     expect(updated).toBeInstanceOf(AdmissionRequest);
//     expect(updated?.id).toBe(saved._id.toString());
//     expect(updated?.documentsAnalysis?.documents).not.toBeNull();
//     expect(updated?.documentsAnalysis?.documents).toHaveLength(2);
//     expect(updated?.documentsAnalysis?.documents?.[0].status).toBe(RequestDocumentStatus.approved);
//     expect(updated?.documentsAnalysis?.documents?.[1].status).toBe(RequestDocumentStatus.approved);
//   });

//   it('should create home visit', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakeHomeVisit = HomeVisitFactory();

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     const created = await repoCreateHomeVisit(saved._id, fakeHomeVisit);

//     expect(created).not.toBeNull();
//     expect(created).toBeInstanceOf(AdmissionRequest);
//     expect(created?.id).toBe(saved._id.toString());
//     expect(created?.homeVisit).not.toBeNull();
//     expect(created?.homeVisit).toBeInstanceOf(HomeVisit);
//     expect(created?.homeVisit?.status).toBe(fakeHomeVisit.status);
//     expect(created?.homeVisit?.responsible).toBe(fakeHomeVisit.responsible);
//     expect(created?.homeVisit?.visitDate).toBe(fakeHomeVisit.visitDate);
//     expect(created?.homeVisit?.isAddressProvidedByApplicant).toBe(fakeHomeVisit.isAddressProvidedByApplicant);
//     expect(created?.homeVisit?.residentOwnershipStatus).toBe(fakeHomeVisit.residentOwnershipStatus);
//     expect(created?.homeVisit?.hasGarage).toBe(fakeHomeVisit.hasGarage);
//     expect(created?.homeVisit?.comments).toBe(fakeHomeVisit.comments);
//     expect(created?.homeVisit?.media).toEqual(fakeHomeVisit.media);
//     expect(created?.homeVisit?.images).toEqual(fakeHomeVisit.images);
//   });

//   it('should update admission request status', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     const updated = await repoUpdateAdmissionRequestStatus(saved._id, AdmissionRequestStatus.rejected);

//     expect(updated).not.toBeNull();
//     expect(updated).toBeInstanceOf(AdmissionRequest);
//     expect(updated?.id).toBe(saved._id.toString());
//     expect(updated?.status).toBe(AdmissionRequestStatus.rejected);
//   });

//   it('should retrieve weekly earnings same week', async () => {
//     const requestId = new Types.ObjectId().toString();
//     const platform = GigPlatform.uber;
//     const currency = CurrencyCode.mxn;
//     const fakeEarning = EarningMongoFactory({
//       requestId,
//       earningDate: new Date('2021-01-02'),
//       amount: 100,
//       countTrips: 1,
//       cashAmount: 1,
//       platform,
//       currency,
//     });
//     const secondEarning = EarningMongoFactory({
//       requestId,
//       earningDate: new Date('2021-01-01'),
//       amount: 200,
//       countTrips: 2,
//       cashAmount: 2,
//       platform,
//       currency,
//     });

//     await EarningMongo.create(fakeEarning);
//     await EarningMongo.create(secondEarning);

//     const foundedWeeks = await repoRetrieveWeeklyEarnings(requestId, platform as GigPlatform);
//     const foundedWeek = foundedWeeks[0];

//     expect(foundedWeeks).not.toBeNull();
//     expect(foundedWeeks).toHaveLength(1);
//     expect(foundedWeek).not.toBeNull();
//     expect(foundedWeek.totalAmount).toBe(303);
//     expect(foundedWeek.totalTrips).toBe(3);
//     expect(foundedWeek.fromDate).toEqual(new Date('2020-12-28'));
//     expect(foundedWeek.toDate).toEqual(new Date('2021-01-03'));
//     expect(foundedWeek.week).toBe(53);
//     expect(foundedWeek.year).toBe(2020);
//     expect(foundedWeek.currency).toBe(currency);
//   });

//   it('should retrieve weekly earnings different week', async () => {
//     const requestId = new Types.ObjectId().toString();
//     const platform = GigPlatform.uber;
//     const currency = CurrencyCode.mxn;
//     const fakeEarning = EarningMongoFactory({
//       requestId,
//       earningDate: new Date('2021-01-02'),
//       amount: 100,
//       countTrips: 1,
//       cashAmount: 1,
//       platform,
//       currency,
//     });
//     const secondEarning = EarningMongoFactory({
//       requestId,
//       earningDate: new Date('2020-12-26'),
//       amount: 200,
//       countTrips: 2,
//       cashAmount: 2,
//       platform,
//       currency,
//     });

//     await EarningMongo.create(fakeEarning);
//     await EarningMongo.create(secondEarning);

//     const foundedWeeks = await repoRetrieveWeeklyEarnings(requestId, platform as GigPlatform);
//     const firstWeek = foundedWeeks[0];
//     const secondWeek = foundedWeeks[1];

//     expect(foundedWeeks).not.toBeNull();
//     expect(foundedWeeks).toHaveLength(2);
//     expect(firstWeek).not.toBeNull();
//     expect(firstWeek.totalAmount).toBe(101);
//     expect(firstWeek.totalTrips).toBe(1);
//     expect(firstWeek.fromDate).toEqual(new Date('2020-12-28'));
//     expect(firstWeek.toDate).toEqual(new Date('2021-01-03'));
//     expect(firstWeek.week).toBe(53);
//     expect(firstWeek.year).toBe(2020);
//     expect(firstWeek.currency).toBe(currency);

//     expect(secondWeek).not.toBeNull();
//     expect(secondWeek.totalAmount).toBe(202);
//     expect(secondWeek.totalTrips).toBe(2);
//     expect(secondWeek.fromDate).toEqual(new Date('2020-12-21'));
//     expect(secondWeek.toDate).toEqual(new Date('2020-12-27'));
//     expect(secondWeek.week).toBe(52);
//     expect(secondWeek.year).toBe(2020);
//     expect(secondWeek.currency).toBe(currency);
//   });

//   it('should save earnings analysis', async () => {
//     const admisisonRequest = AdmissionRequestMongoFactory();
//     const fakeEarningsAnalysis = EarningsAnalysisFactory();

//     await AdmissionRequestMongo.create(admisisonRequest);

//     const saved = await repoSaveEarningsAnalysis(admisisonRequest._id, fakeEarningsAnalysis);

//     const founded = await AdmissionRequestMongo.findById(admisisonRequest._id);

//     expect(saved).not.toBeNull();
//     expect(saved).toBeInstanceOf(AdmissionRequest);
//     expect(saved?.id).toBe(admisisonRequest._id.toString());
//     expect(saved?.earningsAnalysis).not.toBeNull();
//     expect(saved?.earningsAnalysis).toBeInstanceOf(EarningsAnalysis);
//     expect(saved?.earningsAnalysis?.status).toBe(fakeEarningsAnalysis.status);
//     expect(saved?.earningsAnalysis?.totalEarnings).toBe(fakeEarningsAnalysis.totalEarnings);
//     expect(saved?.earningsAnalysis?.platforms).toBe(fakeEarningsAnalysis.platforms);
//     expect(saved?.earningsAnalysis?.earnings).toEqual(fakeEarningsAnalysis.earnings);
//     expect(saved?.earningsAnalysis?.earnings).toHaveLength(2);
//     expect(saved?.earningsAnalysis?.earnings?.[0]).toBeInstanceOf(WeeklyEarning);
//     expect(founded?.earningsAnalysis?.status).toBe(fakeEarningsAnalysis.status);
//     expect(founded?.earningsAnalysis?.totalEarnings).toBe(fakeEarningsAnalysis.totalEarnings);
//     expect(founded?.earningsAnalysis?.platforms).toBe(fakeEarningsAnalysis.platforms);
//     expect(founded?.earningsAnalysis?.earnings).toHaveLength(2);
//   });

//   it('should get paginated admission requests all results', async () => {
//     const fakeAdmissionRequestOne = AdmissionRequestMongoFactory({
//       status: AdmissionRequestStatus.created,
//     });
//     const fakeAdmissionRequestTwo = AdmissionRequestMongoFactory({
//       status: AdmissionRequestStatus.created,
//     });
//     const fakeAdmissionRequestThree = AdmissionRequestMongoFactory({
//       status: AdmissionRequestStatus.created,
//     });

//     await AdmissionRequestMongo.create(fakeAdmissionRequestOne);
//     await AdmissionRequestMongo.create(fakeAdmissionRequestTwo);
//     await AdmissionRequestMongo.create(fakeAdmissionRequestThree);

//     const paginationSearchOptions = PaginationSearchOptionsFactory({
//       page: 1,
//       itemsPerPage: 2,
//     });

//     const q = '';
//     const [number, admissionRequests] = await repoGetPaginatedAdmissionRequests(q, paginationSearchOptions);

//     expect(number).toBe(3);
//     expect(admissionRequests).not.toBeNull();
//     expect(admissionRequests).toHaveLength(2);
//     expect(admissionRequests[0]).toBeInstanceOf(AdmissionRequest);
//     expect(admissionRequests[1]).toBeInstanceOf(AdmissionRequest);
//   });

//   it('should get paginated admission requests filtered results', async () => {
//     const customerOnePersonalData = RequestPersonalDataFactory({
//       firstName: 'John',
//       lastName: 'Doe',
//     });
//     const customerTwoPersonalData = RequestPersonalDataFactory({
//       firstName: 'Laurem',
//       lastName: 'Ipsum',
//     });
//     const fakeAdmissionRequestOne = AdmissionRequestMongoFactory({
//       status: AdmissionRequestStatus.created,
//       personalData: customerOnePersonalData as RequestPersonalDataMongoI,
//     });
//     const fakeAdmissionRequestTwo = AdmissionRequestMongoFactory({
//       status: AdmissionRequestStatus.created,
//       personalData: customerOnePersonalData as RequestPersonalDataMongoI,
//     });
//     const fakeAdmissionRequestThree = AdmissionRequestMongoFactory({
//       status: AdmissionRequestStatus.created,
//       personalData: customerTwoPersonalData as RequestPersonalDataMongoI,
//     });

//     await AdmissionRequestMongo.create(fakeAdmissionRequestOne);
//     await AdmissionRequestMongo.create(fakeAdmissionRequestTwo);
//     await AdmissionRequestMongo.create(fakeAdmissionRequestThree);

//     const paginationSearchOptions = PaginationSearchOptionsFactory({
//       page: 1,
//       itemsPerPage: 3,
//     });

//     const q = customerOnePersonalData.firstName as string;
//     const [number, admissionRequests] = await repoGetPaginatedAdmissionRequests(q, paginationSearchOptions);

//     expect(number).toBe(2);
//     expect(admissionRequests).not.toBeNull();
//     expect(admissionRequests).toHaveLength(2);
//     expect(admissionRequests[0]).toBeInstanceOf(AdmissionRequest);
//     expect(admissionRequests[1]).toBeInstanceOf(AdmissionRequest);
//   });

//   it('should get request document analysis with media', async () => {
//     const document = DocumentMongoFactory();
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakeDocument = RequestDocumentsDetailMongoFactory({
//       mediaId: document._id.toString(),
//     });
//     const fakeDocumentAnalysis = RequestDocumentsAnalysisMongoFactory({
//       documents: [fakeDocument],
//     });
//     fakeAdmissionRequest.documentsAnalysis = fakeDocumentAnalysis as RequestDocumentsAnalysisMongoI;

//     const savedDocument = await DocumentMongo.create(document);
//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     const requestDocumentAnalysis = await repoGetRequestDocumentAnalysisWithMedia(saved._id);
//     const foundedDocuments = requestDocumentAnalysis?.documents;

//     expect(requestDocumentAnalysis).not.toBeNull();
//     expect(requestDocumentAnalysis).toBeInstanceOf(RequestDocumentsAnalysis);
//     expect(foundedDocuments).not.toBeNull();
//     expect(foundedDocuments).toHaveLength(1);
//     expect(foundedDocuments?.[0]).toBeInstanceOf(RequestDocument);
//     expect(foundedDocuments?.[0].media).not.toBeNull();
//     expect(foundedDocuments?.[0].media?.id).toBe(savedDocument._id.toString());
//     expect(foundedDocuments?.[0].media?.url).toBe('https://www.mocked-signed-url.com');
//   });

//   it('should update request document status', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakeDocument = RequestDocumentsDetailMongoFactory({
//       status: RequestDocumentStatus.pending_review,
//       type: AdmissionRequestDocumentType.bank_statement_month_1,
//     });
//     const fakeDocumentAnalysis = RequestDocumentsAnalysisMongoFactory({
//       documents: [fakeDocument],
//     });
//     fakeAdmissionRequest.documentsAnalysis = fakeDocumentAnalysis as RequestDocumentsAnalysisMongoI;

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     await repoUpdateRequestDocumentStatus(
//       saved._id,
//       fakeDocument.type as AdmissionRequestDocumentType,
//       RequestDocumentStatus.approved
//     );

//     const updated = await AdmissionRequestMongo.findById(saved._id);
//     const documents = updated?.documentsAnalysis?.documents;
//     const updatedDocument = documents?.find((doc) => doc.type === fakeDocument.type);

//     expect(updated).not.toBeNull();
//     expect(updatedDocument?.status).toBe(RequestDocumentStatus.approved);
//   });

//   it('should reject request document', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakeDocument = RequestDocumentsDetailMongoFactory({
//       status: RequestDocumentStatus.pending_review,
//       type: AdmissionRequestDocumentType.bank_statement_month_1,
//     });
//     const fakeDocumentAnalysis = RequestDocumentsAnalysisMongoFactory({
//       documents: [fakeDocument],
//     });
//     fakeAdmissionRequest.documentsAnalysis = fakeDocumentAnalysis as RequestDocumentsAnalysisMongoI;

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     await repoRejectRequestDocument(saved._id, fakeDocument.type as AdmissionRequestDocumentType);

//     const updated = await AdmissionRequestMongo.findById(saved._id);
//     const documents = updated?.documentsAnalysis?.documents;
//     const updatedDocument = documents?.find((doc) => doc.type === fakeDocument.type);

//     expect(updated).not.toBeNull();
//     expect(updatedDocument?.status).toBe(RequestDocumentStatus.pending);
//     expect(updatedDocument?.mediaId).toBeNull();
//   });

//   it('should request update request documents analysis status', async () => {
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakeDocument = RequestDocumentsDetailMongoFactory({
//       status: RequestDocumentStatus.pending_review,
//       type: AdmissionRequestDocumentType.bank_statement_month_1,
//     });
//     const fakeDocumentAnalysis = RequestDocumentsAnalysisMongoFactory({
//       status: RequestDocumentsAnalysisStatus.pending,
//       documents: [fakeDocument],
//     });
//     fakeAdmissionRequest.documentsAnalysis = fakeDocumentAnalysis as RequestDocumentsAnalysisMongoI;

//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     await repoRequestUpdateRequestDocumentsAnalysisStatus(saved._id, RequestDocumentsAnalysisStatus.approved);

//     const updated = await AdmissionRequestMongo.findById(saved._id);

//     expect(updated).not.toBeNull();
//     expect(updated?.documentsAnalysis?.status).toBe(RequestDocumentsAnalysisStatus.approved);
//   });

//   it('should get home visit with media', async () => {
//     const document = DocumentMongoFactory();
//     const fakeAdmissionRequest = AdmissionRequestMongoFactory();
//     const fakeHomeVisit = RequestHomeVisitMongoFactory({
//       images: [document._id.toString()],
//     });

//     fakeAdmissionRequest.homeVisit = fakeHomeVisit as RequestHomeVisitMongoI;

//     await DocumentMongo.create(document);
//     const saved = await AdmissionRequestMongo.create(fakeAdmissionRequest);

//     const admissionRequest = await repoGetAdmissionRequestById(saved._id);

//     expect(admissionRequest).not.toBeNull();

//     expect(admissionRequest).toBeInstanceOf(AdmissionRequest);

//     expect(admissionRequest?.id).toBe(saved._id.toString());

//     const homeVisit = await repoGetHomeVisitWithMedia(saved._id);

//     expect(homeVisit).not.toBeNull();
//     expect(homeVisit).toBeInstanceOf(HomeVisit);
//     expect(homeVisit?.media).not.toBeNull();
//     expect(homeVisit?.media).toHaveLength(1);
//     expect(homeVisit?.media?.[0].id).toBe(document._id.toString());
//     expect(homeVisit?.media?.[0].url).toBe('https://www.mocked-signed-url.com');
//   });

//   it('should retrieve palenca platform metric', async () => {
//     const fakeMetric = MetricMongoFactory();

//     const saved = await MetricMongo.create(fakeMetric);

//     const foundedMetric = await repoRetrievePalencaPlatformMetric(
//       saved.requestId.toString(),
//       saved.platform as GigPlatform
//     );

//     expect(foundedMetric).not.toBeNull();
//     expect(foundedMetric).toBeInstanceOf(Metric);
//     expect(foundedMetric?.id).toBe(saved._id.toString());
//   });

//   it('should retrieve events', async () => {
//     const entityId = new Types.ObjectId().toString();
//     const entityType = EntityType.admission_request;
//     const user = UserMongoFactory() as UserMongoI;
//     const fakeEventOne = EventMongoFactory({
//       entityId,
//       entityType: entityType,
//       user,
//     });
//     const fakeEventTwo = EventMongoFactory({
//       entityId,
//       entityType: entityType,
//       user,
//     });
//     await UserMongo.create(user);
//     await EventMongo.create(fakeEventOne);
//     await EventMongo.create(fakeEventTwo);

//     const foundedEvents = await repoRetrieveEvents(entityId, entityType);

//     expect(foundedEvents).not.toBeNull();
//     expect(foundedEvents).toHaveLength(2);
//     expect(foundedEvents[0]).toBeInstanceOf(Event);
//     expect(foundedEvents[1]).toBeInstanceOf(Event);
//   });

//   it('should save event', async () => {
//     const user = UserMongoFactory() as UserMongoI;
//     const fakeEvent = EventFactory({
//       user: user._id.toString(),
//     });

//     await UserMongo.create(user);

//     const saved = await repoSaveEvent(fakeEvent);

//     const foundedEvent = await EventMongo.findById(saved.id);

//     expect(foundedEvent).not.toBeNull();
//     expect(foundedEvent?.id).toBe(saved.id);
//   });

//   it('should save events', async () => {
//     const user = UserMongoFactory() as UserMongoI;
//     const fakeEventOne = EventFactory({
//       user: user._id.toString(),
//     });
//     const fakeEventTwo = EventFactory({
//       user: user._id.toString(),
//     });

//     await UserMongo.create(user);

//     const saved = await repoSaveEvents([fakeEventOne, fakeEventTwo]);

//     const foundedEvents = await EventMongo.find({
//       _id: { $in: [saved[0].id, saved[1].id] },
//     });

//     expect(foundedEvents).not.toBeNull();
//     expect(foundedEvents).toHaveLength(2);
//     expect(foundedEvents[0].id).toBe(saved[0].id);
//     expect(foundedEvents[1].id).toBe(saved[1].id);
//   });

//   it('should save or replace risk analysis data', async () => {
//     const requestId = new Types.ObjectId();
//     const variableName = ScorecardVariableName.age;
//     const toUpdateValue = 32;
//     const fakeRiskAnalysisData = RiskAnalysisDataMongoFactory({
//       requestId,
//     }) as RiskAnalysisDataMongoI;

//     fakeRiskAnalysisData.variables.set(variableName, 12);

//     await RiskAnalysisDataMongo.create(fakeRiskAnalysisData);

//     const saved = await repoSaveOrReplaceRiskAnalysisData(requestId.toString(), variableName, toUpdateValue);

//     const foundedRiskAnalysisData = await RiskAnalysisDataMongo.findById(saved.id);
//     const foundedVariables = foundedRiskAnalysisData?.variables;
//     const foundedAge = foundedVariables?.get(variableName);
//     expect(foundedRiskAnalysisData).not.toBeNull();
//     expect(foundedRiskAnalysisData?.id).toBe(saved.id);
//     expect(foundedAge).toBe(toUpdateValue);
//   });

//   it('should save or replace batch risk analysis data', async () => {
//     const requestId = new Types.ObjectId();
//     const variableName = ScorecardVariableName.age;
//     const toUpdateValue = 32;
//     const fakeRiskAnalysisData = RiskAnalysisDataMongoFactory({
//       requestId,
//     }) as RiskAnalysisDataMongoI;

//     fakeRiskAnalysisData.variables.set(variableName, 12);

//     await RiskAnalysisDataMongo.create(fakeRiskAnalysisData);
//     const map = new Map<ScorecardVariableName, number>();
//     map.set(variableName, toUpdateValue);

//     const saved = await repoSaveOrReplaceBatchRiskAnalysisData(requestId.toString(), map);

//     const foundedRiskAnalysisData = await RiskAnalysisDataMongo.findById(saved.id);
//     const foundedVariables = foundedRiskAnalysisData?.variables;
//     const foundedAge = foundedVariables?.get(variableName);
//     expect(foundedRiskAnalysisData).not.toBeNull();
//     expect(foundedRiskAnalysisData?.id).toBe(saved.id);
//     expect(foundedAge).toBe(toUpdateValue);
//   });

//   it('should get risk analysis data', async () => {
//     const requestId = new Types.ObjectId();
//     const fakeRiskAnalysisData = RiskAnalysisDataMongoFactory({ requestId }) as RiskAnalysisDataMongoI;

//     await RiskAnalysisDataMongo.create(fakeRiskAnalysisData);

//     const foundedRiskAnalysisData = await repoGetRiskAnalysisData(requestId.toString());

//     expect(foundedRiskAnalysisData).not.toBeNull();
//     expect(foundedRiskAnalysisData).toBeInstanceOf(RiskAnalysisData);
//     expect(foundedRiskAnalysisData?.id).toBe(fakeRiskAnalysisData?._id.toString());
//   });

//   it('should get scorecard config', async () => {
//     const foundedScorecardConfig = await repoGetScorecardConfig(ScorecardVersion.v1);

//     expect(foundedScorecardConfig).not.toBeNull();
//     expect(foundedScorecardConfig).toBeInstanceOf(ScorecardConfig);
//   });

//   it('should retrieve metrics for all platforms', async () => {
//     const requestId = new Types.ObjectId();
//     const firstPlatform = GigPlatform.uber;
//     const secondPlatform = GigPlatform.didi;
//     const fakeMetricOne = MetricMongoFactory({
//       requestId,
//       platform: firstPlatform,
//     });
//     const fakeMetricTwo = MetricMongoFactory({
//       requestId,
//       platform: secondPlatform,
//     });

//     await MetricMongo.create(fakeMetricOne);
//     await MetricMongo.create(fakeMetricTwo);

//     const foundedMetrics = await repoRetriveMetricsForAllPlatforms(requestId.toString());

//     expect(foundedMetrics).not.toBeNull();
//     expect(foundedMetrics).toHaveLength(2);
//     expect(foundedMetrics[0]).toBeInstanceOf(Metric);
//     expect(foundedMetrics[1]).toBeInstanceOf(Metric);
//   });

//   // repoSaveRiskAnalysis
//   it('should save risk analysis', async () => {
//     const admisisonRequest = AdmissionRequestMongoFactory();
//     const newScorecard = ScorecardFactory();
//     const savedAdmissionRequest = await AdmissionRequestMongo.create(admisisonRequest);

//     const saved = await repoSaveRiskAnalysis(
//       savedAdmissionRequest._id,
//       RiskAnalysisStatus.completed,
//       newScorecard
//     );

//     const founded = await AdmissionRequestMongo.findById(savedAdmissionRequest._id);

//     expect(saved).not.toBeNull();
//     expect(saved).toBeInstanceOf(AdmissionRequest);
//     expect(saved?.id).toBe(savedAdmissionRequest._id.toString());
//     expect(saved?.riskAnalysis).not.toBeNull();
//     expect(saved?.riskAnalysis).toBeInstanceOf(RiskAnalysis);
//     expect(saved?.riskAnalysis?.status).toBe(RiskAnalysisStatus.completed);
//     expect(saved?.riskAnalysis?.scorecard).toEqual(newScorecard);
//     expect(founded?.riskAnalysis).not.toBeNull();
//   });
// });
