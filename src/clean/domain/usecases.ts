import {
  DRIVER_WEB_APP_URL,
  RUN_HOME_VISITORS_APPOINTMENT_SCHEDULING_REMINDER_CRON,
  GEOCODING_API_KEY,
  IPINFO_TOKEN,
  PALENCA_WIDGET_ID,
  Areas,
  Roles,
  SalesFunnelStage,
  SalesFunnelSubStage,
} from '../../constants';
import {
  AdmissionRequest,
  Document,
  PalencaWebhook,
  RequestDocument,
  RequestPersonalData,
  PalencaAccount,
  RequestPalenca,
  HomeVisit,
  EarningsAnalysis,
  WeeklyEarning,
  Pagination,
  PaginationSearchOptions,
  RequestDocumentsAnalysis,
  Metric,
  Event,
  RiskAnalysis,
  RiskAnalysisData,
  PalencaAccountRetrieval,
  PalencaJobRetrieveEarnings,
  PalencaJobRetrieveMetrics,
  PlatformMetric,
  RequestAvalData,
  RequestLocationData,
  RequestIpInfoData,
} from './entities';
import {
  AdmissionRequestRejectionReason,
  AdmissionRequestStatus,
  GigPlatform,
  MediaStatus,
  MediaType,
  PalencaAccountStatus,
  PalencaWebhookAction,
  EarningsAnalysisStatus,
  RequestDocumentStatus,
  RequestDocumentsAnalysisStatus,
  RequestPersonalDataStepStatus,
  AdmissionRequestDocumentType,
  HomeVisitStatus,
  EntityType,
  ActionType,
  AnalysisStatus,
  ScorecardVersion,
  ScorecardVariableName,
  VehicleType,
  VehicleCondition,
  PalencaRetrievalStatus,
  Country,
  AdmissionRequestDocumentTypeUS,
  AdmissionRequestAdditionalDocumentType,
  DriverSourceType,
  DocumentClassification,
  MLModels,
  DocumentAnalysisStatus,
  DocumentSpanishName,
} from './enums';
import {
  repoInsertAdmissionRequest,
  repoGetAdmissionRequestById,
  repoInsertDocument,
  repoUpdateBatchDocumentStatus,
  repoAddPalencaAccountToAdmissionRequest,
  repoUpdateRequestPersonalData,
  repoUpdateRequestDocuments,
  repoUpdateAdmissionRequestStatus,
  repoUpdatePalencaAccountEarningsRetrievalStatus,
  repoUpdatePalencaAccountMetricsRetrievalStatus,
  repoRetrieveWeeklyEarnings,
  repoSaveEarningsAnalysis,
  repoGetPaginatedAdmissionRequests,
  repoGetRequestDocumentAnalysisWithMedia,
  repoUpdateRequestDocumentStatus,
  repoRejectRequestDocument,
  repoRequestUpdateRequestDocumentsAnalysisStatus,
  repoGetHomeVisitWithMedia,
  repoRetrievePalencaPlatformMetric,
  repoRetrieveEvents,
  repoSaveOrReplaceRiskAnalysisData,
  repoSaveOrReplaceBatchRiskAnalysisData,
  repoRetriveMetricsForAllPlatforms,
  repoUpdatePalencaAccountStatus,
  repoRetriveAllClientByDate,
  repoUpdatePlatformMetric,
  repoUpdateHomeVisit,
  repoUpdateHomeVisitAppointmentSchedulingLinkSendDate,
  getHomeVisitScheduleLinkSendFiveDaysAgo,
  getHomeVisitScheduleLinkSendYesterdayDates,
  getHomeVisitScheduleLinkSendTodayDates,
  repoAddRequestAvalData,
  repoSaveAllModelResults,
  repoAddUserLocationData as repoAddLocationData,
  repoSaveDocumentsResults,
  repoGetPlatformMetric,
} from '../data/mongoRepositories';
import {
  AdmissionRequestNotFoundException,
  DocumentAnalysisFailedException,
  AllRequiredDocumentsAreNotApproved,
  ModelExecutionFailedException,
  NotAllDocumentsApprovedException,
  RequestNotInDocumentsAnalysisStatusException,
  RequestNotInRiskAnalysisStatusException,
  RequestNotInSocialAnalysisStatusException,
} from '../errors/exceptions';
import { repoUploadMedia } from '../data/s3Repositories';
import { repoAddRetrieveEarningsJob, repoAddRetrieveMetricsJob } from '../data/queueRepositories';
import { determineEarningsAnalysisStatus } from '../lib/earnings';
import { logger } from '../lib/logger';
import { generatePagination } from '../lib/pagination';
import { Types } from 'mongoose';
import {
  sendHomeVisitAppointmentApologyEmail,
  sendHomeVisitAppointmentCancelEmail,
  sendHomeVisitAppointmentFinishEmail,
  sendHomeVisitAppointmentNoShowMessageEmail,
  sendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo,
  sendHomeVisitAppointmentReminderEmailOneNightAgo,
  sendHomeVisitAppointmentScheduledEmail,
  sendHomeVisitAppointmentScheduleLinkEmail,
  sendHomeVisitAppointmentScheduleLinkEmailReminder,
  sendHomeVisitFormApprovalEmail,
  sendHomeVisitLocationVerificationEmail,
  sendReminderEmailToCustomerAfterRegistering,
  sendHomeImageUploadEmail,
} from '../../modules/platform_connections/emailFunc';
import { calculateEarningsAnalysis as updatedImplementationOfEarningsAnalysis } from '../../services/socialScoring/calculateEarningsAnalysis';
import { dealUpdate } from '../../services/hubspot';
import HubspotBatch from '../../models/hubspotBatch';
import { getMeetingIdFromMeetingLink, removeFile } from '../lib/utils';
import {
  sendHomeVisitAppointmentScheduleLink,
  sendHomeVisitAppointmentScheduledMessage,
  sendHomeVisitAppointmentFinishMessage,
  sendHomeVisitApprovalOrRejectionMessage,
  sendHomeVisitAppointmentReminderMessageOneNightAgoCron,
  sendHomeVisitAppointmentReminderMessageAboutFiveMinutes,
  sendHomeVisitAppointmentScheduleLinkReminder,
  sendHomeVisitAppointmentNoShowMessage,
  sendHomeVisitAppointmentApologyMessage,
  sendLocationGatheringMessageToUser,
  sendHomeVisitAppointmentCancelMessage,
  sendHomeImageUploadMessageToUser,
} from '@/services/onboarding/sendHilos';
import { Appointment } from '@/models/appointment';
import { calculateModelScoreService } from '@/modules/AI/services/ai.service';
import { analyzeMultipleDocuments } from '@/modules/AI/services/documentsAnalysis/documentsAnalysis.service';
import axios from 'axios';
import { UserMongo } from '../../models/userSchema';
import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';
import { BlockLeadAssignation } from '@/models/blockLeadAssignationSchema';
import { auditTrail } from '@/modules/AuditTrail/services/auditTrail.service';
import { AuthUser } from '@/types&interfaces/types';
import {
  sendHomeVisitAppointmentCancelFCMNotification,
  sendHomeVisitAppointmentScheduledFCMNotification,
  sendHomeVisitAppointmentSchedulingLinkFCMNotification,
} from '@/modules/Calendar/utils/homeVisitAppointmentNotifications';
import { DateTime } from 'luxon';

export async function getNextAgent(): Promise<Types.ObjectId | undefined> {
  // Step 1: Get all agents by email
  const allAgents = await UserMongo.find({
    role: Roles.agent,
    area: Areas.sales,
    isVerified: true,
    isActive: true,
  });

  if (allAgents.length === 0) {
    logger.error('No verified agents available for assignment');
    return undefined;
  }

  const now = new Date();

  // Step 2: Find all blocked agents at this moment
  const blockedAgents = await BlockLeadAssignation.find({
    agent: { $in: allAgents.map((a) => a._id) },
    blockedFrom: { $lte: now },
    blockedUntil: { $gte: now },
  }).distinct('agent');

  // Step 3: Filter out blocked agents
  const availableAgents = allAgents.filter((agent) => !blockedAgents.some((id) => id.equals(agent._id)));

  if (availableAgents.length === 0) {
    throw new Error('No agents are currently available — all are blocked');
  }

  // Step 4: Find last assigned agent to continue round-robin
  const lastRequest = await AdmissionRequestMongo.findOne(
    { agentId: { $in: availableAgents.map((a) => a._id) } },
    { agentId: 1 }
  ).sort({ createdAt: -1 });

  let nextIndex = 0;

  if (lastRequest?.agentId) {
    const lastIndex = availableAgents.findIndex((agent) => agent._id.equals(lastRequest.agentId));

    if (lastIndex !== -1) {
      nextIndex = (lastIndex + 1) % availableAgents.length;
    }
  }

  const nextAgent = availableAgents[nextIndex];

  if (!nextAgent) {
    logger.error('Next agent unexpectedly not found');
    return undefined;
  }

  return nextAgent._id;
}

export const updateStageSubStage = async (
  requestId: string,
  stage: string,
  subStage: string | null,
  authUser: AuthUser | undefined
  // eslint-disable-next-line max-params
) => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!admissionRequest) {
    logger.error(`[updateRequest] Admission request not found`);
    throw new AdmissionRequestNotFoundException();
  }
  admissionRequest.stage = stage;
  admissionRequest.subStage = subStage;
  await admissionRequest.save();
  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: requestId!,
      entityType: EntityType.admission_request,
      actionType: ActionType['admission_request.stage_substage'],
      message: `Actualización de la stage/sub-stage de solicitud de admisión`,
      metadata: {
        userEmail: authUser.email,
        stage,
        subStage,
      },
    });
  }
  return admissionRequest;
};

export const markAdmissionRequestAsLostAndNoActivity = async () => {
  // Get the current date and time
  const now = new Date();

  // Create a date 60 days ago
  const sixtyDaysAgo = new Date(now);
  sixtyDaysAgo.setDate(now.getDate() - 60);
  const admissionRequestArr = await AdmissionRequestMongo.find({ updatedAt: { $lt: sixtyDaysAgo } });
  // Use Promise.all with map to wait for all async operations
  await Promise.all(
    admissionRequestArr.map(async (admissionRequest) => {
      if (
        admissionRequest.stage === SalesFunnelStage.DELIVERED_VEHICLE ||
        admissionRequest.stage === SalesFunnelStage.LOST ||
        admissionRequest.createdAt < new Date('2025-07-28') // do not consider old admisson request with no stage substage values
      ) {
        return; // Skip saving
      } else {
        admissionRequest.stage = SalesFunnelStage.LOST;
        admissionRequest.subStage = SalesFunnelSubStage.NO_ACTIVITY_60_DAYS;
        await admissionRequest.save();
      }
    })
  );
};

export const createAdmissionRequest = async (
  requestPersonalData: RequestPersonalData,
  authUser: AuthUser | undefined,
  clientIpAddress?: string,
  vehicleType?: VehicleType,
  source?: string
  // eslint-disable-next-line max-params
): Promise<AdmissionRequest> => {
  const admissionRequestId = new Types.ObjectId().toHexString();

  const requestPalenca = new RequestPalenca({
    widgetId: PALENCA_WIDGET_ID,
    externalId: admissionRequestId,
    accounts: [],
  });

  const documents =
    requestPersonalData?.country === Country.us
      ? AdmissionRequestDocumentTypeUS
      : { ...AdmissionRequestDocumentType, ...AdmissionRequestAdditionalDocumentType };

  const pendigDocuments = Object.values(documents).map((documentType) => {
    return new RequestDocument({
      type: documentType,
      status: RequestDocumentStatus.pending,
      mediaId: null,
    });
  });

  const documentsAnalysis = new RequestDocumentsAnalysis({
    status: RequestDocumentsAnalysisStatus.pending,
    documents: pendigDocuments,
  });

  const personalData = new RequestPersonalData({
    ...requestPersonalData,
    status: RequestPersonalDataStepStatus.pending,
    firstName: requestPersonalData.firstName,
    lastName: requestPersonalData.lastName,
    email: requestPersonalData.email,
    phone: requestPersonalData.phone,
  });

  const earningsAnalysis = new EarningsAnalysis({
    status: EarningsAnalysisStatus.pending,
  });

  const riskAnalysis = new RiskAnalysis({
    status: AnalysisStatus.pending,
    scorecardVersion: ScorecardVersion.v1, // Hardcoded for now, sue me :3
  });

  let decodedSource: string | null = null;
  if (source) {
    decodedSource = Object(DriverSourceType)[source];
  }

  let agentId: Types.ObjectId | undefined;
  // check if its not brazil to get agentid
  if (requestPersonalData.country === Country.mx) {
    agentId = await getNextAgent();
  }

  const newAdmissionRequest = new AdmissionRequest({
    status: AdmissionRequestStatus.created,
    palenca: requestPalenca,
    documentsAnalysis,
    personalData,
    earningsAnalysis,
    riskAnalysis,
    source: decodedSource,
    clientIpAddress,
    agentId,
  });

  const repoAdmissionRequest = await repoInsertAdmissionRequest(newAdmissionRequest);
  if (repoAdmissionRequest) {
    await updateStageSubStage(repoAdmissionRequest.id!, SalesFunnelStage.PENDING, null, authUser);
  }

  logger.info(
    // eslint-disable-next-line prettier/prettier
    `[createAdmissionRequest] Admission request inserted ${repoAdmissionRequest.id
    } with the following details Palenca: ${JSON.stringify(requestPalenca)}, Documents: ${JSON.stringify(
      documents
    )}, Personal data: ${JSON.stringify(
      personalData
    )}, the source is ${decodedSource}, and Ip address is ${clientIpAddress}`
  );

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: repoAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['admission_request.created'],
      message: `Solicitud de admisión creada`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  const defaultVehicleType = vehicleType || VehicleType.car;
  console.log('defaultVehicleType', defaultVehicleType);
  await repoSaveOrReplaceRiskAnalysisData(
    repoAdmissionRequest.id!,
    ScorecardVariableName.vehicle_type,
    defaultVehicleType
  );

  logger.info(
    `[createAdmissionRequest] Stored RiskAnalysisData for vehicle_type ${defaultVehicleType} for admission request ${repoAdmissionRequest.id}`
  );
  console.log('FINISH createAdmissionRequest', repoAdmissionRequest);
  return repoAdmissionRequest;
};

export const getAdmissionRequestByIdOrThrow = async (id: string): Promise<AdmissionRequest> => {
  const admissionRequest = await repoGetAdmissionRequestById(id);

  if (!admissionRequest) {
    logger.info(`[getAdmissionRequestByIdOrThrow] Admission request not found for id ${id}`);
    throw new AdmissionRequestNotFoundException();
  }
  return admissionRequest;
};

export const addRetrieveEarningsJob = async (job: PalencaJobRetrieveEarnings): Promise<null> => {
  // Mark the accounts as queued to avoid duplicate jobs
  await repoAddRetrieveEarningsJob(job);
  await repoUpdatePalencaAccountEarningsRetrievalStatus(
    job.requestId,
    job.accountId,
    job.platform,
    PalencaRetrievalStatus.queued
  );

  return null;
};

export const addRetrieveMetricsJob = async (job: PalencaJobRetrieveMetrics): Promise<null> => {
  // Mark the accounts as queued to avoid duplicate jobs
  await repoAddRetrieveMetricsJob(job);
  await repoUpdatePalencaAccountMetricsRetrievalStatus(
    job.requestId,
    job.accountId,
    job.platform,
    PalencaRetrievalStatus.queued
  );

  return null;
};

export const evaluatePalencaWebhook = async (palencaWebhook: PalencaWebhook): Promise<null> => {
  const webhookActions = [
    PalencaWebhookAction['earnings.updated'],
    PalencaWebhookAction['profile.updated'],
    PalencaWebhookAction['login.success'],
  ];
  if (!webhookActions.includes(palencaWebhook.webhookAction)) {
    logger.info(`[evaluatePalencaWebhook] Webhook action not supported ${palencaWebhook.webhookAction}`);
    return null;
  }

  const admissionRequest = await repoGetAdmissionRequestById(palencaWebhook.externalId);

  // If the admission request does not exist we do nothing
  if (!admissionRequest) {
    logger.info(`[evaluatePalencaWebhook] Admission request not found for id ${palencaWebhook.externalId}`);
    return null;
  }

  // If the status is not created or earnings_analysis, do nothing
  if (
    admissionRequest.status !== AdmissionRequestStatus.created &&
    admissionRequest.status !== AdmissionRequestStatus.earnings_analysis &&
    admissionRequest.status !== AdmissionRequestStatus.documents_analysis
  ) {
    logger.info(
      `[evaluatePalencaWebhook] Admission request status not supported ${admissionRequest.status} for id ${palencaWebhook.externalId}`
    );
    return null;
  }

  const palenca = admissionRequest.palenca;

  const palencaAccount = palenca.accounts.find(
    (account) =>
      account.accountId === palencaWebhook.accountId && account.platform === palencaWebhook.platform
  );

  // This means the palenca has successfully logged in to their account and the data extraction process has started.
  if (!palencaAccount && palencaWebhook.webhookAction === PalencaWebhookAction['login.success']) {
    // If we have already an account with the same platform and accountId we do nothing

    const existingAccount = palenca.accounts.find(
      (account) =>
        account.accountId === palencaWebhook.accountId && account.platform === palencaWebhook.platform
    );

    if (existingAccount) {
      logger.info(
        `[evaluatePalencaWebhook] Palenca account already exists for account ${palencaWebhook.accountId} and platform ${palencaWebhook.platform}`
      );
      return null;
    }

    const earnings = new PalencaAccountRetrieval({
      status: PalencaRetrievalStatus.pending,
    });

    const metrics = new PalencaAccountRetrieval({
      status: PalencaRetrievalStatus.pending,
    });

    const newAccount = new PalencaAccount({
      accountId: palencaWebhook.accountId,
      platform: palencaWebhook.platform,
      earnings,
      metrics,
      status: PalencaAccountStatus.pending,
      createdAt: new Date(),
    });

    await repoAddPalencaAccountToAdmissionRequest(admissionRequest.id!, newAccount);
    logger.info(`[evaluatePalencaWebhook] Palenca account added to admission request ${admissionRequest.id}`);
  }

  // Earnings information has been created or updated and we can we can retrieve them
  if (admissionRequest && palencaWebhook.webhookAction === PalencaWebhookAction['earnings.updated']) {
    // If the account has already been retrieved we do nothing
    if (palencaAccount && palencaAccount.earnings.status === PalencaRetrievalStatus.success) {
      logger.info(
        `[evaluatePalencaWebhook] Earnings already retrieved for account ${palencaAccount.accountId}`
      );
      return null;
    }

    // If the account has pending status we queue a job to retrieve the earnings
    if (palencaAccount && palencaAccount.earnings.status === PalencaRetrievalStatus.pending) {
      const palencaJobRetrieveEarnings = new PalencaJobRetrieveEarnings({
        accountId: palencaWebhook.accountId,
        platform: palencaWebhook.platform,
        requestId: admissionRequest.id!,
      });

      logger.info(
        `[evaluatePalencaWebhook] Adding job to retrieve earnings for account ${palencaAccount.accountId} and platform ${palencaAccount.platform}`
      );
      await addRetrieveEarningsJob(palencaJobRetrieveEarnings);
    }
  }

  if (palencaWebhook.webhookAction === PalencaWebhookAction['profile.updated']) {
    // If the account has already been retrieved we do nothing
    if (palencaAccount && palencaAccount.metrics.status === PalencaRetrievalStatus.success) {
      logger.info(
        `[evaluatePalencaWebhook] Metrics already retrieved for account ${palencaAccount.accountId}`
      );
      return null;
    }

    // If the account has pending status we queue a job to retrieve the metrics
    if (palencaAccount && palencaAccount.metrics.status === PalencaRetrievalStatus.pending) {
      const palencaJobRetrieveMetrics = new PalencaJobRetrieveMetrics({
        accountId: palencaWebhook.accountId,
        platform: palencaWebhook.platform,
        requestId: admissionRequest.id!,
      });

      logger.info(
        `[evaluatePalencaWebhook] Adding job to retrieve metrics for account ${palencaAccount.accountId} and platform ${palencaAccount.platform}`
      );
      await addRetrieveEarningsJob(palencaJobRetrieveMetrics);
    }
  }

  return null;
};

export const retrieveMostRecent12WeeksEarnings = async (
  requestId: string,
  platform?: GigPlatform
): Promise<WeeklyEarning[]> => {
  const weeklyEarnings = await repoRetrieveWeeklyEarnings(requestId, platform);
  logger.info(
    `[retrieveMostRecent12WeeksEarnings] Fetched ${weeklyEarnings.length} earnings for admission request ${requestId} and platform ${platform}`
  );
  const sortedWeeks = weeklyEarnings.sort((a, b) => {
    // First compare by year
    if (a.year > b.year) return -1;
    if (a.year < b.year) return 1;

    // If the years are equal, compare by week
    return b.week - a.week;
  });

  const mostRecent12Weeks = sortedWeeks.slice(0, 12);
  return mostRecent12Weeks;
};

export const calculateEarningsAnalysis = async (requestId: string): Promise<AdmissionRequest> => {
  //Primero se trae el id de la base de datos
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the earnings analysis is not pending we do nothing
  // if (currentAdmissionRequest.earningsAnalysis?.status !== EarningsAnalysisStatus.pending) {
  //   //valida si esta en estado pendiente
  //   logger.info(
  //     `[calculateEarningsAnalysis] Earnings analysis status not pending for admission request ${requestId}`
  //   );
  //   return currentAdmissionRequest;
  // }

  const platforms = currentAdmissionRequest.palenca.accounts.length;
  //revisa cuantas plataformas tiene el usuario

  // Retrieve earnings of all platforms
  const mostRecent12Weeks = await retrieveMostRecent12WeeksEarnings(requestId);

  const totalEarnings = mostRecent12Weeks.reduce((acc, curr) => acc + curr.totalAmount, 0);

  const preQualification = determineEarningsAnalysisStatus(totalEarnings);
  //Precalificacion por ganancias, 90000 aprobado, 70000 aprobado con condiciones, menor de 70000 rechazado
  logger.info(
    `[calculateEarningsAnalysis] Pre qualification status ${preQualification} for admission request ${requestId}`
  );
  const earningsAnalysis = new EarningsAnalysis({
    totalEarnings,
    status: preQualification,
    earnings: mostRecent12Weeks,
    platforms,
  });

  const updated = await repoSaveEarningsAnalysis(requestId, earningsAnalysis);

  return updated;
};

export const checkAndCalculateEarningsAnalysis = async (requestId: string): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the request is not in earnings analysis status we do nothing
  if (currentAdmissionRequest.status !== AdmissionRequestStatus.earnings_analysis) {
    logger.info(
      `[checkAndCalculateEarningsAnalysis] Admission request status not earnings_analysis for id ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // If earnings analysis is not pending we do nothing
  if (currentAdmissionRequest.earningsAnalysis?.status !== EarningsAnalysisStatus.pending) {
    logger.info(
      `[checkAndCalculateEarningsAnalysis] Earnings analysis status not pending for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Make sure we retreived all the earnings for each account before calculating the earnings analysis
  const palencaAccounts = currentAdmissionRequest.palenca.accounts;

  const earningsRetrievalStatus = palencaAccounts.every(
    (account) => account.earnings.status === PalencaRetrievalStatus.success
  );

  if (!earningsRetrievalStatus) {
    logger.info(
      `[checkAndCalculateEarningsAnalysis] Not all earnings have been retrieved for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Calcualte and save the earnings analysis
  logger.info(
    `[checkAndCalculateEarningsAnalysis] Calculating earnings analysis for admission request ${requestId}`
  );
  const updated = await calculateEarningsAnalysis(requestId);

  // If the earnings analysis is approved or approved with conditions we update the request status as documents_analysis
  // If the earnings analysis is rejected we update the request status as rejected and set reasons earnings_analysis
  if (updated.earningsAnalysis) {
    if (
      updated.earningsAnalysis.status === EarningsAnalysisStatus.approved ||
      updated.earningsAnalysis.status === EarningsAnalysisStatus.approved_with_conditions
    ) {
      logger.info(
        `[checkAndCalculateEarningsAnalysis] Updating admission request status as documents_analysis for admission request ${requestId}`
      );
      await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.documents_analysis);

      // Store the RiskAnalysisData for earnings_last_12_weeks
      const earningsLast12Weeks = updated.earningsAnalysis.totalEarnings!;
      await repoSaveOrReplaceRiskAnalysisData(
        requestId,
        ScorecardVariableName.earnings_last_12_weeks,
        earningsLast12Weeks
      );
      logger.info(
        `[checkAndCalculateEarningsAnalysis] Stored RiskAnalysisData for earnings_last_12_weeks ${earningsLast12Weeks} for admission request ${requestId}`
      );

      // Store the RiskAnalysisData for gig_platforms
      const numberOfGigPlatforms = updated.earningsAnalysis.platforms!;
      await repoSaveOrReplaceRiskAnalysisData(
        requestId,
        ScorecardVariableName.gig_platforms,
        numberOfGigPlatforms
      );

      logger.info(
        `[checkAndCalculateEarningsAnalysis] Stored RiskAnalysisData for gig_platforms ${numberOfGigPlatforms} for admission request ${requestId}`
      );

      // Store the RiskAnalysisData from vehicle_condition
      const vehicleCondition =
        updated.earningsAnalysis.status === EarningsAnalysisStatus.approved_with_conditions
          ? VehicleCondition.used
          : VehicleCondition.new;

      await repoSaveOrReplaceRiskAnalysisData(
        requestId,
        ScorecardVariableName.vehicle_condition,
        vehicleCondition
      );
    }

    if (updated.earningsAnalysis.status === EarningsAnalysisStatus.rejected) {
      logger.info(
        `[checkAndCalculateEarningsAnalysis] Updating admission request status as rejected for admission request ${requestId}`
      );
      await repoUpdateAdmissionRequestStatus(
        requestId,
        AdmissionRequestStatus.rejected,
        AdmissionRequestRejectionReason.earnings_analysis
      );
    }
  }

  return updated;
};

export const calculateMetricsAnalysis = async (requestId: string): Promise<null> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  // If the request is approved or rejected we do nothing
  if (
    current.status === AdmissionRequestStatus.approved ||
    current.status === AdmissionRequestStatus.rejected
  ) {
    logger.info(
      `[calculateMetricsAnalysis] Admission request status approved or rejected for id ${requestId}`
    );
    return null;
  }

  const palencaAccounts = current.palenca.accounts;

  const metricsRetrievalStatus = palencaAccounts.every(
    (account) => account.metrics.status === PalencaRetrievalStatus.success
  );

  if (!metricsRetrievalStatus) {
    logger.info(
      `[calculateMetricsAnalysis] Not all metrics have been retrieved for admission request ${requestId}`
    );
    return null;
  }

  const metrics = await repoRetriveMetricsForAllPlatforms(requestId);

  // We calculate the total lifetime trips by adding all the lifetime trips
  const lifeTimeCompletedTrips = metrics.reduce((acc, curr) => acc + curr.lifetimeTrips, 0);

  // We calculate the average acceptance rate by adding all the acceptance rates and dividing by the number of platforms
  const averageAcceptanceRate = metrics.reduce((acc, curr) => acc + curr.acceptanceRate, 0) / metrics.length;

  // We calculate the average cancellation rate by adding all the cancellation rates and dividing by the number of platforms
  const averageCancellationRate =
    metrics.reduce((acc, curr) => acc + curr.cancellationRate, 0) / metrics.length;

  // We calculate the average rating by adding all the ratings and dividing by the number of platforms
  const averageRating = metrics.reduce((acc, curr) => acc + curr.rating, 0) / metrics.length;

  // We calculate the time since first trip by getting the greatest time of all the platforms
  const greatestTimeSinceFirstTrip = metrics.reduce((max, currentElem) => {
    return currentElem.timeSinceFirstTrip > max ? currentElem.timeSinceFirstTrip : max;
  }, 0);

  const variables: Map<ScorecardVariableName, number | string> = new Map();
  variables.set(ScorecardVariableName.life_time_completed_trips, lifeTimeCompletedTrips);
  variables.set(ScorecardVariableName.percentage_acceptance_rate, averageAcceptanceRate);
  variables.set(ScorecardVariableName.percentage_cancellation_rate, averageCancellationRate);
  variables.set(ScorecardVariableName.average_rating, averageRating);
  variables.set(ScorecardVariableName.days_since_first_trip, greatestTimeSinceFirstTrip);

  await repoSaveOrReplaceBatchRiskAnalysisData(requestId, variables);

  logger.info(
    `[calculateMetricsAnalysis] Stored RiskAnalysisData Metrics for admission request ${requestId}`
  );

  return null;
};

export const checkAndCalculateMetrics = async (requestId: string): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the request is has approved or rejected status we do nothing
  if (
    currentAdmissionRequest.status === AdmissionRequestStatus.approved ||
    currentAdmissionRequest.status === AdmissionRequestStatus.rejected
  ) {
    logger.info(
      `[checkAndCalculateMetrics] Admission request status approved or rejected for id ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Make sure we retreived all the metrics for each account before calculating the metrics analysis
  const palencaAccounts = currentAdmissionRequest.palenca.accounts;

  const metricsRetrievalStatus = palencaAccounts.every(
    (account) => account.metrics.status === PalencaRetrievalStatus.success
  );

  if (!metricsRetrievalStatus) {
    logger.info(
      `[checkAndCalculateMetrics] Not all metrics have been retrieved for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Calculate and save the metrics as RiskAnalysisData
  logger.info(`[checkAndCalculateMetrics] Calculating metrics analysis for admission request ${requestId}`);
  await calculateMetricsAnalysis(requestId);

  return currentAdmissionRequest;
};

export const checkAndMarkPalencaAccountAsSuccess = async (
  requestId: string,
  accountId: string,
  platform: GigPlatform
): Promise<null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const palencaAccount = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.accountId === accountId && account.platform === platform
  );

  if (!palencaAccount) {
    return null;
  }

  if (palencaAccount.status === PalencaAccountStatus.success) {
    return null;
  }

  // If both earnings and metrics have been retrieved we mark the account as success
  if (
    palencaAccount.earnings.status === PalencaRetrievalStatus.success &&
    palencaAccount.metrics.status === PalencaRetrievalStatus.success
  ) {
    logger.info(
      `[checkAndMarkPalencaAccountAsSuccess] Updating palenca account status as success for account ${accountId}`
    );
    await repoUpdatePalencaAccountStatus(
      requestId,
      accountId,
      palencaAccount.platform,
      PalencaAccountStatus.success
    );
  }

  // If any of the earnings or metrics have error status we mark the account as error
  if (
    palencaAccount.earnings.status === PalencaRetrievalStatus.error ||
    palencaAccount.metrics.status === PalencaRetrievalStatus.error
  ) {
    logger.info(
      `[checkAndMarkPalencaAccountAsSuccess] Updating palenca account status as error for account ${accountId}`
    );
    await repoUpdatePalencaAccountStatus(
      requestId,
      accountId,
      palencaAccount.platform,
      PalencaAccountStatus.error
    );
  }

  return null;
};

export const updateRequestPersonalData = async (
  requestId: string,
  requestPersonalData: RequestPersonalData,
  user: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  const saved = await repoUpdateRequestPersonalData(current.id!, requestPersonalData);

  // If the birthdate has been updated we store the RiskAnalysisData as age
  if (requestPersonalData.birthdate) {
    const birthdate = new Date(requestPersonalData.birthdate);
    const age = new Date().getFullYear() - birthdate.getFullYear();
    await repoSaveOrReplaceRiskAnalysisData(current.id!, ScorecardVariableName.age, age);
    logger.info(
      `[updateRequestPersonalData] Stored RiskAnalysisData for age ${age} for admission request ${current.id!}`
    );
  }

  if (user) {
    const oldValues: Record<string, any> = {};
    const updateFields = Object.keys(requestPersonalData).reduce<Record<string, any>>((acc, key) => {
      const typedKey = key as keyof RequestPersonalData;
      if (
        requestPersonalData[typedKey] !== undefined &&
        requestPersonalData[typedKey] !== null &&
        requestPersonalData[typedKey] !== ''
      ) {
        if (requestPersonalData[typedKey] !== current.personalData[typedKey]) {
          acc[key] = requestPersonalData[typedKey];
          oldValues[key] = current.personalData[typedKey];
        }
      }
      return acc;
    }, {});

    if (Object.keys(updateFields).length > 0) {
      await auditTrail.logCustomEvent({
        userId: user.userId,
        entityId: requestId,
        entityType: EntityType.admission_request,
        actionType: ActionType['personal_data.updated'],
        message: 'Datos personales actualizados',
        metadata: {
          userEmail: user.email,
          oldValue: oldValues,
          newValue: updateFields,
        },
      });
    }
  }

  return saved;
};

export const uploadMedia = async ({
  file,
  mediaType,
}: {
  file: Express.Multer.File;
  mediaType: MediaType;
}): Promise<Document> => {
  const [path, mimeType] = await repoUploadMedia({ file, mediaType });
  const document = new Document({
    fileName: file.originalname,
    path,
    mimeType,
    type: mediaType,
    status: MediaStatus.pending,
  });

  logger.info(`[uploadMedia] media document created ${JSON.stringify(document)}`);

  const saved = await repoInsertDocument(document);
  await removeFile(file.path);

  return saved;
};

export const verifyStageForAllDocuments = async (requestId: string, authUser: AuthUser | undefined) => {
  // adding logic for updating stage values to document analysis based on all documents uploaded
  const updatedAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const updatedRequestDocuments = updatedAdmissionRequest.documentsAnalysis.documents;

  if (updatedAdmissionRequest.stage === SalesFunnelStage.APPROVED_EARNINGS) {
    const requiredDocs = [];
    Object.values(AdmissionRequestDocumentType).forEach((docType) => {
      const reqDoc = updatedRequestDocuments.find(
        (document) => document.type === docType && document.mediaId !== null
      );
      if (reqDoc) {
        requiredDocs.push(reqDoc);
      }
    });
    const requiredDocuments: string[] = Object.values(AdmissionRequestDocumentType);
    if (requiredDocs.length === requiredDocuments.length) {
      await updateStageSubStage(requestId, SalesFunnelStage.DOCUMENT_ANALYSIS, null, authUser);
    }
  }

  if (updatedAdmissionRequest.stage === SalesFunnelStage.APPROVED_IN_HOME_VISIT) {
    const addtionalDocs = [];
    const addtionDocumentType = [
      AdmissionRequestAdditionalDocumentType.drivers_license_back,
      AdmissionRequestAdditionalDocumentType.drivers_license_front,
      AdmissionRequestAdditionalDocumentType.garage_photo,
      AdmissionRequestAdditionalDocumentType.proof_of_tax_situation,
      AdmissionRequestAdditionalDocumentType.selfie_photo,
    ];
    Object.values(addtionDocumentType).forEach((docType) => {
      const addDoc = updatedRequestDocuments.find(
        (document) => document.type === docType && document.mediaId !== null
      );
      if (addDoc) {
        addtionalDocs.push(addDoc);
      }
    });
    if (addtionalDocs.length === addtionDocumentType.length) {
      await updateStageSubStage(
        requestId,
        SalesFunnelStage.CONTRACT_GENERATION,
        SalesFunnelSubStage.ADDITIONAL_DOCUMENTS,
        authUser
      );
    }
  }
};

export const updateRequestDocuments = async (
  requestId: string,
  updateDocuments: RequestDocument[],
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const currentRequestDocuments = currentAdmissionRequest.documentsAnalysis.documents;

  const updates: RequestDocument[] = [];
  const deletions: string[] = [];

  // Check if there is already a mediaId in each document field of the currentRequestDocuments
  // If there is set it the new save to update array and the delete media id in deletions

  for (const updateDocument of updateDocuments) {
    const currentDocument = currentRequestDocuments.find((document) => document.type === updateDocument.type);
    logger.info(
      `[updateRequestDocuments] document ${updateDocument.type} with status ${updateDocument.status}`
    );
    let message = `Documento ${DocumentSpanishName[updateDocument.type]} subido`;
    if (currentDocument) {
      if (currentDocument.mediaId) {
        deletions.push(currentDocument.mediaId);
        message = `Documento ${DocumentSpanishName[updateDocument.type]} resubido`;
      }
    }

    // Always clear analysisResult when updating document to avoid carrying forward previous analysis
    const documentToUpdate = { ...updateDocument };
    documentToUpdate.analysisResult = undefined;
    logger.info(`[updateRequestDocuments] Cleared analysisResult for document ${updateDocument.type}`);
    updates.push(documentToUpdate);

    if (authUser) {
      await auditTrail.logCustomEvent({
        userId: authUser?.userId,
        entityId: requestId!,
        entityType: EntityType.admission_request,
        actionType: ActionType['document.uploaded'],
        message: message,
        metadata: {
          userEmail: authUser?.email,
        },
      });
    }
  }

  if (updates.length > 0) {
    const updateMediaIds = updates.map((update) => update.mediaId!);
    await repoUpdateBatchDocumentStatus(updateMediaIds, MediaStatus.active);
  }

  if (deletions.length > 0) {
    await repoUpdateBatchDocumentStatus(deletions, MediaStatus.deleted);
  }

  if (updates.length > 0 || deletions.length > 0) {
    const updated = await repoUpdateRequestDocuments(requestId, updates);
    await verifyStageForAllDocuments(requestId, authUser);
    return updated;
  }

  return currentAdmissionRequest;
};

export const notifyCustomerAboutHomeVisitFormApproval = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitApprovalOrRejectionMessage({
      requestId: requestId,
      phone: phone!,
      type: 'homeVisitApproval',
    });

    await sendHomeVisitFormApprovalEmail({
      email: email!,
      requestId: requestId,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitFormApproval] - Home visit form approval message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

const updateHomeVisitStepsStatusOnHubspot = async (
  requestId: string,
  homeVisitStepsStatus: Record<string, string>
) => {
  const incompleteSteps: Record<string, string> = {};
  for (const [key, value] of Object.entries(homeVisitStepsStatus)) {
    if (value === 'incomplete' || !value) {
      incompleteSteps[key] = 'incomplete';
    }
  }

  const incompleteStepsNames = Object.keys(incompleteSteps);
  if (incompleteStepsNames.length === 0) {
    logger.info(`[updateHomeVisitStepsStatusOnHubspot] No incomplete steps found for requestId ${requestId}`);
    return;
  }

  const hubspotBatch = await HubspotBatch.findOne({
    requestId,
  });
  if (!hubspotBatch || !hubspotBatch.dealId) {
    logger.info(
      `[updateHomeVisitStepsStatusOnHubspot] No Hubspot batch found for requestId ${requestId} or dealId is missing`
    );
    return;
  }
  await dealUpdate({
    dealId: hubspotBatch.dealId,
    properties: {
      data_pendiente_en_visita: incompleteStepsNames.join(', '),
      dealstage: '984001181',
    },
  });
};

export const updateHomeVisit = async (
  requestId: string,
  homeVisit: HomeVisit,
  user: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  try {
    await repoUpdateBatchDocumentStatus(homeVisit.images as Array<string>, MediaStatus.active);
    await repoUpdateBatchDocumentStatus(
      homeVisit.proofOfPropertyOwnership as Array<string>,
      MediaStatus.active
    );
  } catch (error) {
    logger.error(`[updateHomeVisit] Error updating home visit for admission request ${requestId}`);
  }

  logger.info(`[updateHomeVisit] Updating home visit for admission request ${requestId}`);

  const saved = await repoUpdateHomeVisit(requestId, homeVisit);

  if (homeVisit.status === HomeVisitStatus.pending) {
    logger.info(
      `[updateHomeVisit] Updating admission request status as pending for admission request ${requestId}`
    );
    await repoUpdateAdmissionRequestStatus(
      requestId,
      AdmissionRequestStatus.home_visit,
      AdmissionRequestRejectionReason.home_visit
    );

    if (user) {
      await auditTrail.logCustomEvent({
        userId: user.userId,
        entityId: currentAdmissionRequest.id!,
        entityType: EntityType.admission_request,
        actionType: ActionType['home_visit.pending'],
        message: `Visita domiciliaria pendiente`,
        metadata: {
          userEmail: user.email,
        },
      });
    }
  }

  if (homeVisit.status === HomeVisitStatus.approved) {
    logger.info(
      `[createHomeVisit] Updating admission request status as final_evaluation for admission request ${requestId}`
    );

    await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.social_analysis);

    await notifyCustomerAboutHomeVisitFormApproval(requestId);

    await updateStageSubStage(requestId, SalesFunnelStage.APPROVED_IN_HOME_VISIT, null, user);

    await verifyStageForAllDocuments(requestId, user);

    if (user) {
      await auditTrail.logCustomEvent({
        userId: user.userId,
        entityId: currentAdmissionRequest.id!,
        entityType: EntityType.admission_request,
        actionType: ActionType['home_visit.approved'],
        message: `Visita domiciliaria aprobada`,
        metadata: {
          userEmail: user.email,
        },
      });
    }
  }

  if (homeVisit.status === HomeVisitStatus.rejected) {
    logger.info(
      `[createHomeVisit] Updating admission request status as rejected for admission request ${requestId}`
    );
    await repoUpdateAdmissionRequestStatus(
      requestId,
      AdmissionRequestStatus.rejected,
      AdmissionRequestRejectionReason.home_visit
    );

    updateStageSubStage(requestId, SalesFunnelStage.REJECTED_IN_HOME_VISIT, null, user);

    const hubspotBatch = await HubspotBatch.findOne({
      requestId,
    });

    if (hubspotBatch && hubspotBatch.dealId) {
      await dealUpdate({
        dealId: hubspotBatch.dealId,
        properties: {
          dealstage: 'closedlost',
        },
      });
    }

    await sendHomeVisitApprovalOrRejectionMessage({
      requestId: requestId,
      phone: currentAdmissionRequest.personalData.phone!,
      type: 'homeVisitRejection',
    });

    if (user) {
      await auditTrail.logCustomEvent({
        userId: user.userId,
        entityId: currentAdmissionRequest.id!,
        entityType: EntityType.admission_request,
        actionType: ActionType['home_visit.rejected'],
        message: `Visita domiciliaria rechazada`,
        metadata: {
          userEmail: user.email,
        },
      });
      await auditTrail.logCustomEvent({
        userId: user.userId,
        entityId: currentAdmissionRequest.id!,
        entityType: EntityType.admission_request,
        actionType: ActionType['admission_request.rejected'],
        message: `Solicitud de admisión rechazada`,
        metadata: {
          userEmail: user.email,
        },
      });
    }
  }

  const homeVisitStepsStatus = saved.homeVisit?.homeVisitStepsStatus;
  if (!homeVisitStepsStatus) {
    return saved;
  }
  await updateHomeVisitStepsStatusOnHubspot(requestId, homeVisitStepsStatus);

  const existingHomeVisit = currentAdmissionRequest.homeVisit;

  if (user && existingHomeVisit) {
    const oldValues: Record<string, any> = {};
    const updateFields: Record<string, any> = {};

    Object.keys(homeVisit).forEach((key) => {
      const typedKey = key as keyof typeof homeVisit;
      if (homeVisit[typedKey] !== undefined) {
        if (JSON.stringify(homeVisit[typedKey]) !== JSON.stringify(existingHomeVisit[typedKey])) {
          updateFields[key] = homeVisit[typedKey];
          oldValues[key] = existingHomeVisit[typedKey];
        }
      }
    });

    if (Object.keys(updateFields).length > 0) {
      await auditTrail.logCustomEvent({
        userId: user.userId,
        entityId: requestId,
        entityType: EntityType.admission_request,
        actionType: ActionType['home_visit.updated'],
        message: 'Datos visita a domicilio actualizados',
        metadata: {
          userEmail: user.email,
          oldValue: oldValues,
          newValue: updateFields,
        },
      });
    }
  }

  return saved;
};

export const addPalencaAccountToAdmissionRequest = async (
  requestId: string,
  platform: GigPlatform,
  accountId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the account already exists we do nothing
  const accountExists = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.accountId === accountId && account.platform === platform
  );

  if (accountExists) {
    return currentAdmissionRequest;
  }

  const earnings = new PalencaAccountRetrieval({
    status: PalencaRetrievalStatus.pending,
  });

  const metrics = new PalencaAccountRetrieval({
    status: PalencaRetrievalStatus.pending,
  });

  const palencaAccount = new PalencaAccount({
    accountId,
    platform,
    earnings,
    metrics,
    status: PalencaAccountStatus.pending,
    createdAt: new Date(),
  });

  logger.info(
    `[addPalencaAccountToAdmissionRequest] Adding palenca account to admission request ${requestId} for account ${accountId} and platform ${platform}`
  );
  const updated = await repoAddPalencaAccountToAdmissionRequest(requestId, palencaAccount);

  if (!updated) {
    throw new AdmissionRequestNotFoundException();
  }

  return updated;
};

export const searchPaginatedAdmissionRequests = async (
  country: string,
  q: string,
  options: PaginationSearchOptions,
  status?: string,
  excludeConverted?: boolean
  // eslint-disable-next-line max-params
): Promise<[Pagination, AdmissionRequest[]]> => {
  const [totalItems, entities] = await repoGetPaginatedAdmissionRequests(
    country,
    q,
    options,
    status,
    excludeConverted
  );

  const pagination = generatePagination(options.page!, totalItems, options.itemsPerPage!);

  return [pagination, entities];
};

export const getRequestDocumentsAnalysis = async (
  requestId: string,
  documentClassification: DocumentClassification
): Promise<RequestDocumentsAnalysis> => {
  return repoGetRequestDocumentAnalysisWithMedia(requestId, documentClassification);
};

export const sendHomeVisitAppointmentSendedulingLink = async ({
  requestId,
  phone,
  name,
}: {
  requestId: string;
  phone: string;
  name: string;
}) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);
    const {
      personalData: { email },
    } = currentAdmissionRequest;
    const response = await sendHomeVisitAppointmentScheduleLink({
      phone,
      name,
      type: 'appointmentScheduler',
      requestId: requestId,
    });
    logger.info(
      `[sendHomeVisitAppointmentSendedulingLink] - Appointment scheduler link sent with id ${response.id} to clientId ${requestId}`
    );

    await sendHomeVisitAppointmentScheduleLinkEmail({
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit`,
      requestId: requestId,
    });

    await sendHomeVisitAppointmentSchedulingLinkFCMNotification({
      userId: requestId,
      appointmentScheduleLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit`,
    });

    await repoUpdateHomeVisitAppointmentSchedulingLinkSendDate(requestId, new Date());
  } catch (error) {
    logger.info(
      `[sendHomeVisitAppointmentSendedulingLink] - Failed to send appointment scheduler page link to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const approveRequestDocument = async (
  requestId: string,
  documentType: AdmissionRequestDocumentType,
  authUser: AuthUser | undefined
): Promise<null> => {
  await repoUpdateRequestDocumentStatus(requestId, documentType, RequestDocumentStatus.approved);

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: requestId!,
      entityType: EntityType.admission_request,
      actionType: ActionType['document.approved'],
      message: `Documento ${DocumentSpanishName[documentType]} aprobado`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  return null;
};

export const rejectRequestDocument = async (
  requestId: string,
  documentType: AdmissionRequestDocumentType,
  authUser: AuthUser | undefined
): Promise<null> => {
  await repoRejectRequestDocument(requestId, documentType);
  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: requestId!,
      entityType: EntityType.admission_request,
      actionType: ActionType['document.rejected'],
      message: `Documento ${DocumentSpanishName[documentType]} rechazado`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }
  return null;
};

// Integration into the executeDocumentAnalysis function
export const executeDocumentsAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined,
  documentClassification: DocumentClassification
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const documentsToAnalyse = await repoGetRequestDocumentAnalysisWithMedia(requestId, documentClassification);

  // Filter documents to only include those with pending_review status
  const pendingReviewDocuments = documentsToAnalyse.documents.filter(
    (doc) =>
      doc.analysisResult?.status !== DocumentAnalysisStatus.valid &&
      doc.analysisResult?.status !== DocumentAnalysisStatus.invalid
  );

  logger.info(
    `[executeDocumentsAnalysis] Found ${pendingReviewDocuments.length} documents in pending_review status for request ${requestId}`
  );

  if (pendingReviewDocuments.length > 0) {
    logger.info(
      `[executeDocumentsAnalysis] Processing ${pendingReviewDocuments.length} documents for request ${requestId}`
    );

    // Wait for all document analyses to complete
    const documentResults = await analyzeMultipleDocuments(pendingReviewDocuments, currentAdmissionRequest);

    // Save all document results in a single database update
    const updatedAdmissionRequest = await repoSaveDocumentsResults(requestId, documentResults);

    // Check for analysis errors
    const errorMessages: string[] = [];
    documentResults.forEach(({ documentType, result }) => {
      if (result.status === DocumentAnalysisStatus.error) {
        errorMessages.push(`${documentType} analysis error: ${result.validationErrors.join(', ')}`);
      }
    });

    if (errorMessages.length > 0) {
      throw new DocumentAnalysisFailedException(errorMessages.join(', '));
    }

    if (authUser) {
      await auditTrail.logCustomEvent({
        userId: authUser.userId,
        entityId: updatedAdmissionRequest.id!,
        entityType: EntityType.admission_request,
        actionType: ActionType['documents_analysis.completed'],
        message: `Análisis automatizado de documentos completado`,
        metadata: {
          userEmail: authUser.email,
        },
      });
    }

    return updatedAdmissionRequest;
  }

  logger.info(`[executeDocumentsAnalysis] No pending documents found for processing in request ${requestId}`);
  return currentAdmissionRequest;
};

export const calculateRiskAnalysis = async (requestId: string): Promise<AdmissionRequest> => {
  let current = await getAdmissionRequestByIdOrThrow(requestId);

  // If admission request is not on risk analysis status we do nothing
  if (current.status !== AdmissionRequestStatus.risk_analysis) {
    logger.info(
      `[calculateRiskAnalysis] Admission request status not risk_analysis for admission request ${requestId}`
    );
    throw new RequestNotInRiskAnalysisStatusException();
  }

  // Determine which models need to be calculated
  const modelsToProcess: MLModels[] = [];

  // Check rideshare score
  const hasCompletedRideshareScore =
    current.modelScores?.[MLModels.RIDESHARE_PERFORMANCE]?.status &&
    current.modelScores[MLModels.RIDESHARE_PERFORMANCE].status === AnalysisStatus.completed;

  if (!hasCompletedRideshareScore) {
    modelsToProcess.push(MLModels.RIDESHARE_PERFORMANCE);
  } else {
    logger.info(
      `[calculateRiskAnalysis] Rideshare performance already calculated for admission request ${requestId}`
    );
  }

  // Check financial score
  const hasCompletedFinancialScore =
    current.modelScores?.[MLModels.FINANCIAL_ASSESSMENT]?.status &&
    current.modelScores[MLModels.FINANCIAL_ASSESSMENT].status === AnalysisStatus.completed;

  if (!hasCompletedFinancialScore) {
    modelsToProcess.push(MLModels.FINANCIAL_ASSESSMENT);
  } else {
    logger.info(
      `[calculateRiskAnalysis] Financial assessment already calculated for admission request ${requestId}`
    );
  }

  if (modelsToProcess.length > 0) {
    logger.info(
      `[calculateRiskAnalysis] Processing ${modelsToProcess.length} models in parallel for request ${requestId}`
    );

    // Execute all required model calculations in parallel
    const modelPromises = modelsToProcess.map((modelName) =>
      // We execute the fetch and processing but NOT the database save
      calculateModelScoreService(modelName, current)
    );

    // Wait for all model calculations to complete
    const modelResults = await Promise.all(modelPromises);

    // Save all model results in a single database update
    current = await repoSaveAllModelResults(requestId, modelResults);
  }

  logger.info(`[calculateRiskAnalysis] Risk analysis completed for admission request ${requestId}`);

  return current;
};

export const approveRequestDocumentAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.documents_analysis) {
    logger.info(
      `[approveRequestDocumentAnalysis] Admission request status not documents_analysis for id ${requestId}`
    );
    throw new RequestNotInDocumentsAnalysisStatusException();
  }

  const documentsAnalysis = currentAdmissionRequest.documentsAnalysis;
  let isRequiredDocumentsApproved: boolean;

  if (currentAdmissionRequest.personalData.country === Country.us) {
    isRequiredDocumentsApproved = documentsAnalysis.documents.every((document: RequestDocument) => {
      if (document.status === RequestDocumentStatus.approved) {
        return true;
      }
      logger.info(
        `[approveRequestDocumentAnalysis] ${document.type} is not approved for admission request ${requestId}`
      );
      return false;
    });
  } else {
    const requiredDocuments: string[] = Object.values(AdmissionRequestDocumentType);
    isRequiredDocumentsApproved = requiredDocuments.every((documentType: string) => {
      const requiredDocument = documentsAnalysis.documents.find(
        (document: RequestDocument) => document.type === documentType
      );
      if (requiredDocument && requiredDocument.status === RequestDocumentStatus.approved) {
        return true;
      }
      logger.info(
        `[approveRequestDocumentAnalysis] ${requiredDocument?.type} is not approved for admission request ${requestId}`
      );
      return false;
    });
  }

  if (!isRequiredDocumentsApproved) {
    throw new NotAllDocumentsApprovedException();
  }

  await repoRequestUpdateRequestDocumentsAnalysisStatus(requestId, RequestDocumentsAnalysisStatus.approved);

  const updated = await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.risk_analysis);

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: updated.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['documents_analysis.approved'],
      message: `Análisis de documentos aprobado`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  const isDeal = await HubspotBatch.findOne({ requestId }).lean();
  if (isDeal?.dealId) {
    console.log('entro');
    await dealUpdate({
      dealId: isDeal?.dealId,
      properties: { dealstage: 'qualifiedtobuy' },
    });
  }

  // We start the risk analysis process
  const updatedAdmissionRequest = await calculateRiskAnalysis(requestId);

  // Extract error messages from model scores
  const rideshareModel = updatedAdmissionRequest.modelScores?.[MLModels.RIDESHARE_PERFORMANCE];
  const financialModel = updatedAdmissionRequest.modelScores?.[MLModels.FINANCIAL_ASSESSMENT];

  let errorMessages: string[] = [];

  if (
    rideshareModel?.status === AnalysisStatus.error &&
    (rideshareModel.modelResult as { message: string })?.message
  ) {
    const message = (rideshareModel.modelResult as { message: string }).message;
    errorMessages.push(`Rideshare analysis error: ${message}`);
  }

  if (
    financialModel?.status === AnalysisStatus.error &&
    (financialModel.modelResult as { message: string })?.message
  ) {
    errorMessages.push(
      `Financial analysis error: ${(financialModel.modelResult as { message: string })?.message}`
    );
  }

  if (currentAdmissionRequest.personalData.country === Country.mx) {
    await sendHomeVisitAppointmentSendedulingLink({
      requestId: currentAdmissionRequest.id!,
      phone: currentAdmissionRequest.personalData.phone!,
      name: currentAdmissionRequest.personalData.firstName!,
    });
  }

  if (errorMessages.length > 0) {
    throw new ModelExecutionFailedException(errorMessages.join(', '));
  }

  return updated;
};

export const executeSocialAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  let currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.social_analysis) {
    logger.info(`[executeSocialAnalysis] Admission request status not social_analysis for id ${requestId}`);
    throw new RequestNotInSocialAnalysisStatusException();
  }

  // Determine which models need processing
  const modelsToProcess = Object.values(MLModels).filter((modelName) => {
    const currentScore = currentAdmissionRequest.modelScores?.[modelName];
    return !currentScore || currentScore.status !== AnalysisStatus.completed;
  });

  if (modelsToProcess.length > 0) {
    logger.info(
      `[executeSocialAnalysis] Processing ${modelsToProcess.length} models for request ${requestId}`
    );

    // Execute all required model calculations in parallel
    const modelPromises = modelsToProcess.map((modelName) =>
      // We execute the fetch and processing but NOT the database save
      calculateModelScoreService(modelName, currentAdmissionRequest)
    );

    // Wait for all model calculations to complete
    const modelResults = await Promise.all(modelPromises);

    // Save all model results in a single database update
    currentAdmissionRequest = await repoSaveAllModelResults(requestId, modelResults);
  }

  // Check all model scores for any errors
  let errorMessages: string[] = [];

  for (const modelName of Object.values(MLModels)) {
    const modelScore = currentAdmissionRequest.modelScores?.[modelName];
    if (
      modelScore?.status === AnalysisStatus.error &&
      (modelScore.modelResult as { message: string })?.message
    ) {
      const message = (modelScore.modelResult as { message: string }).message;
      errorMessages.push(`${modelName} analysis error: ${message}`);
    }
  }

  if (errorMessages.length > 0) {
    throw new ModelExecutionFailedException(errorMessages.join(', '));
  }

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['social_analysis.executed'],
      message: `Análisis social ejecutado`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  return currentAdmissionRequest;
};

export const approveRequestSocialAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.social_analysis) {
    logger.info(
      `[approveRequestSocialAnalysis] Admission request status not social_analysis for id ${requestId}`
    );
    throw new RequestNotInSocialAnalysisStatusException();
  }

  currentAdmissionRequest.documentsAnalysis.documents.forEach((document) => {
    if (document.status !== RequestDocumentStatus.approved) {
      logger.info(
        `[approveRequestSocialAnalysis] All the required documents are not approved yet for id ${requestId}`
      );
      throw new AllRequiredDocumentsAreNotApproved();
    }
  });

  await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.approved);

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['social_analysis.approved'],
      message: `Análisis social aprobado`,
      metadata: {
        userEmail: authUser.email,
      },
    });

    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['admission_request.approved'],
      message: `Solicitud de admisión aprobada`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  return currentAdmissionRequest;
};

export const rejectRequestSocialAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.social_analysis) {
    logger.info(
      `[rejectRequestSocialAnalysis] Admission request status not social_analysis for id ${requestId}`
    );
    throw new RequestNotInSocialAnalysisStatusException();
  }

  await repoUpdateAdmissionRequestStatus(
    requestId,
    AdmissionRequestStatus.rejected,
    AdmissionRequestRejectionReason.social_analysis
  );

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['social_analysis.rejected'],
      message: `Análisis social rechazado`,
      metadata: {
        userEmail: authUser.email,
      },
    });

    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['admission_request.rejected'],
      message: `Solicitud de admisión rechazada`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  return currentAdmissionRequest;
};

export const getHomeVisit = async (requestId: string): Promise<HomeVisit> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  return repoGetHomeVisitWithMedia(currentAdmissionRequest.id!);
};

export const retrievePalencaPlatformMetric = async ({
  requestId,
  platform,
}: {
  requestId: string;
  platform: GigPlatform;
}): Promise<Metric | null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const palencaAccount = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.platform === platform
  );

  if (!palencaAccount) {
    return null;
  }

  const metric = await repoRetrievePalencaPlatformMetric(requestId, platform);

  return metric;
};

export const retrieveEvents = async (entityType: EntityType, entityId: string): Promise<Event[]> => {
  const events = await repoRetrieveEvents(entityType, entityId);

  return events;
};

export const saveOrReplaceRiskAnalysisData = async (
  requestId: string,
  variable: ScorecardVariableName,
  value: number | string
): Promise<RiskAnalysisData | null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the admission request is approved or rejected we do nothing
  if (
    currentAdmissionRequest.status === AdmissionRequestStatus.approved ||
    currentAdmissionRequest.status === AdmissionRequestStatus.rejected
  ) {
    logger.info(
      `[saveOrReplaceRiskAnalysisData] Admission request status approved or rejected for admission request ${requestId}`
    );
    return null;
  }

  const updated = await repoSaveOrReplaceRiskAnalysisData(requestId, variable, value);

  return updated;
};

export const approveRiskAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  // If admission request is not on risk analysis status we do nothing
  if (current.status !== AdmissionRequestStatus.risk_analysis) {
    logger.info(
      `[approveRiskAnalysis] Admission request status not risk_analysis for admission request ${requestId}`
    );
    throw new RequestNotInRiskAnalysisStatusException();
  }

  // If the risk analysis is not completed we do nothing
  // if (current.riskAnalysis?.status !== RiskAnalysisStatus.completed) {
  //   logger.info(
  //     `[approveRiskAnalysis] Risk analysis status not completed for admission request ${requestId}`
  //   );
  //   throw new RiskAnalysisNotCompletedException();
  // }

  // We can start the home visit process
  // Check if home visit is already approved, move directly to social analysis
  const homeVisitApproved = current.homeVisit?.status === HomeVisitStatus.approved;
  const isCountryUS = current.personalData.country === Country.us;

  const updated = await repoUpdateAdmissionRequestStatus(
    requestId,
    isCountryUS
      ? AdmissionRequestStatus.social_analysis
      : homeVisitApproved
        ? AdmissionRequestStatus.social_analysis
        : AdmissionRequestStatus.home_visit
  );

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: updated.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['risk_analysis.approved'],
      message: `Análisis de riesgo aprobado`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  return updated;
};

export const rejectRiskAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  // If admission request is not on risk analysis status we do nothing
  if (current.status !== AdmissionRequestStatus.risk_analysis) {
    logger.info(
      `[rejectRiskAnalysis] Admission request status not risk_analysis for admission request ${requestId}`
    );
    throw new RequestNotInRiskAnalysisStatusException();
  }

  const updated = await repoUpdateAdmissionRequestStatus(
    requestId,
    AdmissionRequestStatus.rejected,
    AdmissionRequestRejectionReason.risk_analysis
  );

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: updated.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['risk_analysis.rejected'],
      message: `Análisis de riesgo rechazado`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  return updated;
};

export const rejectRequestDocumentAnalysis = async (
  requestId: string,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.documents_analysis) {
    logger.info(
      `[rejectRequestDocumentAnalysis] Admission request status not documents_analysis for id ${requestId}`
    );
    throw new RequestNotInDocumentsAnalysisStatusException();
  }

  await repoRequestUpdateRequestDocumentsAnalysisStatus(requestId, RequestDocumentsAnalysisStatus.rejected);

  const updated = await repoUpdateAdmissionRequestStatus(
    requestId,
    AdmissionRequestStatus.rejected,
    AdmissionRequestRejectionReason.documents_analysis
  );

  if (authUser) {
    await auditTrail.logCustomEvent({
      userId: authUser.userId,
      entityId: updated.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['documents_analysis.rejected'],
      message: `Análisis de documentos rechazado`,
      metadata: {
        userEmail: authUser.email,
      },
    });
  }

  return updated;
};

export const retryAnalysis = async (requestId: string, modelName: MLModels): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // Check if model exists in admission request and get its status
  const modelStatus = currentAdmissionRequest.modelScores?.[modelName]?.status;

  if (modelStatus === AnalysisStatus.pending) {
    logger.info(`[retryAnalysis] Model ${modelName} is still pending for admission request ${requestId}`);
    return currentAdmissionRequest;
  }

  if (modelStatus === AnalysisStatus.completed) {
    logger.info(`[retryAnalysis] Model ${modelName} is already completed for admission request ${requestId}`);
    return currentAdmissionRequest;
  }

  // Only retry if status is error or null(meaning the model not created, indicating an older record)
  if (modelStatus === AnalysisStatus.error || !modelStatus) {
    logger.info(`[retryAnalysis] Retrying model ${modelName} calculation for admission request ${requestId}`);

    // Use the same parallelized approach as other functions
    try {
      // Execute the model calculation without saving to DB
      const modelResult = await calculateModelScoreService(modelName, currentAdmissionRequest);

      // Save the result in a single database operation
      const updated = await repoSaveAllModelResults(requestId, [modelResult]);

      return updated;
    } catch (error: any) {
      logger.error(
        `[retryAnalysis] Error retrying model ${modelName} for request ${requestId}: ${error.message}`,
        error
      );
      throw error;
    }
  }

  logger.info(`[retryAnalysis] Invalid model status ${modelStatus} for admission request ${requestId}`);
  return currentAdmissionRequest;
};

export const notificationEmailForCustomersWhoHasNotStartedOnboardingWithInTwoDays = async () => {
  try {
    logger.info(
      'Sending auto notification reminder email to customers who has not started onboarding within two days'
    );

    const lastTwoDaysDate = new Date();
    lastTwoDaysDate.setUTCDate(lastTwoDaysDate.getUTCDate() - 2);

    const lastTwoDaysDateStartDate = new Date(lastTwoDaysDate);
    lastTwoDaysDateStartDate.setUTCHours(0, 0, 0, 0);

    const lastTwoDaysDateEndDate = new Date(lastTwoDaysDate);
    lastTwoDaysDateEndDate.setUTCHours(23, 59, 59, 999);

    const registeredCustomersOflastTwoDays = await repoRetriveAllClientByDate(
      lastTwoDaysDateStartDate,
      lastTwoDaysDateEndDate
    );

    logger.info(
      `Total no of Registered customers of last two days: ${registeredCustomersOflastTwoDays.length}`
    );

    await Promise.allSettled(
      registeredCustomersOflastTwoDays.map(async (customer) => {
        const { email, firstName, lastName, country } = customer.personalData;
        try {
          await sendReminderEmailToCustomerAfterRegistering({
            customerEmail: email as string,
            customerName: `${firstName} ${lastName ? lastName : ''}`,
            customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${customer.id}`,
            country: country as Country,
          });
          logger.info(`Successfully sent auto notification onboarding reminder email to ${email}`);
        } catch (err) {
          logger.error(
            `Error occured while sending auto notification onboarding reminder email to ${email}   `
          );
        }
      })
    );
  } catch (err) {
    logger.error('Error occured while sending auto notification reminder email to customers', err);
  }
};

export const updatePlatformMetric = async (
  requestId: string,
  platformMetricPayload: PlatformMetric,
  authUser: AuthUser
) => {
  try {
    logger.info(`Updating platform metric for admission request ${requestId}`);

    const savedMetric = await repoGetPlatformMetric(requestId);
    if (savedMetric) {
      if (savedMetric.acceptanceRate !== platformMetricPayload.acceptanceRate) {
        await auditTrail.logCustomEvent({
          userId: authUser.userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          message: `${platformMetricPayload.platform} Tasa de aceptación actualizado de ${savedMetric.acceptanceRate} a ${platformMetricPayload.acceptanceRate}`,
          metadata: {
            userEmail: authUser.email,
          },
        });
      }
      if (savedMetric.cancellationRate !== platformMetricPayload.cancellationRate) {
        await auditTrail.logCustomEvent({
          userId: authUser.userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          message: `${platformMetricPayload.platform} Tasa de cancelación actualizado de ${savedMetric.cancellationRate} a ${platformMetricPayload.cancellationRate}`,
          metadata: {
            userEmail: authUser.email,
          },
        });
      }
      if (savedMetric.rating !== platformMetricPayload.rating) {
        await auditTrail.logCustomEvent({
          userId: authUser.userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          message: `${platformMetricPayload.platform} Clasificación actualizado de ${savedMetric.rating} a ${platformMetricPayload.rating}`,
          metadata: {
            userEmail: authUser.email,
          },
        });
      }
      if (savedMetric.lifetimeTrips !== platformMetricPayload.lifetimeTrips) {
        await auditTrail.logCustomEvent({
          userId: authUser.userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          message: `${platformMetricPayload.platform} Viajes totales actualizado de ${savedMetric.lifetimeTrips} a ${platformMetricPayload.lifetimeTrips}`,
          metadata: {
            userEmail: authUser.email,
          },
        });
      }
      if (savedMetric.timeSinceFirstTrip !== platformMetricPayload.timeSinceFirstTrip) {
        await auditTrail.logCustomEvent({
          userId: authUser.userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          message: `${platformMetricPayload.platform} Tiempo desde el primer viaje actualizado de ${savedMetric.timeSinceFirstTrip} a ${platformMetricPayload.timeSinceFirstTrip}`,
          metadata: {
            userEmail: authUser.email,
          },
        });
      }
    }

    const platformMetric = await repoUpdatePlatformMetric(requestId, platformMetricPayload);
    await updatedImplementationOfEarningsAnalysis(requestId);

    return platformMetric;
  } catch (err: any) {
    logger.error(
      `Error occured while updating platform metric for admission request ${requestId}. ${err?.message}`,
      err?.stack
    );
    throw err;
  }
};

export const notifyCustomerAboutHomeVisitAppointmentBooking = async (
  requestId: string,
  appointmentMetaData: {
    meetingLink: string;
    date: Date;
    startTime: Date;
  }
) => {
  try {
    const { meetingLink, date, startTime } = appointmentMetaData;

    const admissionRequest = await repoGetAdmissionRequestById(requestId);
    const {
      personalData: { phone, email },
    } = admissionRequest;

    const formattedDate = DateTime.fromJSDate(date).toFormat('dd-MM-yyyy');
    const formattedStartTime = DateTime.fromJSDate(startTime).toFormat('hh:mm a');

    await sendHomeVisitAppointmentScheduledMessage({
      date: formattedDate,
      startTime: formattedStartTime,
      requestId: requestId,
      phone: phone!,
      meetingLink: getMeetingIdFromMeetingLink(meetingLink)!,
      type: 'homeVisitAppointmentScheduled',
    });

    const appointmentRescheduleLink = `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`;

    await sendHomeVisitAppointmentScheduledEmail({
      date: formattedDate,
      startTime: formattedStartTime,
      email: email!,
      customerWebAppLink: appointmentRescheduleLink,
      meetingLink,
      requestId: requestId,
    });

    await sendHomeVisitAppointmentScheduledFCMNotification({
      userId: requestId,
      date: formattedDate,
      time: formattedStartTime,
      meetingLink: meetingLink!,
      rescheduleAppointmentLink: appointmentRescheduleLink,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentBooking] Home visit appointment scheduled message failed to send to clientId ${requestId} due to error: ${error}`
    );
    throw error;
  }
};

export const notifyCustomerAboutHomeVisitAppointmentOneNightAgo = async (
  requestId: string,
  appointmentMetaData: {
    date: string;
    startTime: string;
    meetingLink: string;
  }
) => {
  try {
    const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);
    const {
      personalData: { phone, firstName },
    } = admissionRequest;

    const { date, startTime, meetingLink } = appointmentMetaData;
    await sendHomeVisitAppointmentReminderMessageOneNightAgoCron({
      name: firstName!,
      date,
      startTime,
      meetingLink: getMeetingIdFromMeetingLink(meetingLink)!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentReminderOneNightAgo',
    });

    await sendHomeVisitAppointmentReminderEmailOneNightAgo({
      email: admissionRequest.personalData.email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
      date,
      startTime,
      meetingLink,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentOneNightAgo] - Home visit appointment reminder one night ago message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentAboutFiveMinutesBefore = async (
  requestId: string,
  appointmentMetaData: {
    date: string;
    startTime: string;
    meetingLink: string;
  }
) => {
  try {
    const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);
    const {
      personalData: { phone, firstName, email },
    } = admissionRequest;

    const { date, startTime, meetingLink } = appointmentMetaData;
    await sendHomeVisitAppointmentReminderMessageAboutFiveMinutes({
      name: firstName!,
      date,
      startTime,
      meetingLink: getMeetingIdFromMeetingLink(meetingLink)!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentReminderAboutFiveMinutes',
    });

    await sendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo({
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
      date,
      startTime,
      meetingLink,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentAboutFiveMinutesBefore] - Home visit appointment reminder about five minutes ago message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentFinish = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { firstName, phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentFinishMessage({
      name: firstName!,
      phone: phone!,
      type: 'homeVisitAppointmentFinish',
    });

    await sendHomeVisitAppointmentFinishEmail({
      email: email!,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentFinish] - Home visit appointment finish message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const homeVisitAppointmnetScheduleFirstReminder = async () => {
  try {
    const todayHomeVisitRequests = await getHomeVisitScheduleLinkSendTodayDates();

    logger.info(
      `[homeVisitAppointmnetScheduleFirstReminder]: Sending home visit appointment scheduling links to ${todayHomeVisitRequests.length} clients with applications approved today.`
    );

    await Promise.all(
      todayHomeVisitRequests.map(async (admissionRequest) => {
        const {
          _id: requestId,
          personalData: { firstName, phone, email },
        } = admissionRequest;
        try {
          const existingAppointment = await Appointment.findOne({
            admissionRequestId: requestId,
          });
          if (existingAppointment) {
            logger.info(
              `[homeVisitAppointmnetScheduleFirstReminder] - Home visit appointment already exist for clientId ${requestId}`
            );
            return;
          }
          await sendHomeVisitAppointmentScheduleLinkReminder({
            phone,
            name: firstName,
            type: 'appointmentSchedulerReminder',
            requestId: requestId,
          });

          await sendHomeVisitAppointmentScheduleLinkEmailReminder({
            name: firstName,
            email: email,
            requestId: requestId,
            customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit`,
          });
        } catch (error: any) {
          logger.error(
            `[homeVisitAppointmnetScheduleFirstReminder] - Home visit appointment scheduling link failed to send to clientId ${requestId},`,
            {
              message: error.message,
              stack: error.stack,
            }
          );
        }
      })
    );
  } catch (error: any) {
    logger.error(
      `[homeVisitAppointmnetScheduleFirstReminder] - Error occured while sending home visit appointment scheduling link to clients`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const homeVisitAppointmnetScheduleSecondReminder = async () => {
  try {
    const yesterdayHomeVisitRequests = await getHomeVisitScheduleLinkSendYesterdayDates();

    logger.info(
      `[homeVisitAppointmnetScheduleSecondReminder]: Sending home visit appointment scheduling links to ${yesterdayHomeVisitRequests.length} clients with applications approved yesterday.`
    );

    await Promise.all(
      yesterdayHomeVisitRequests.map(async (admissionRequest) => {
        const {
          _id: requestId,
          personalData: { firstName, phone, email },
        } = admissionRequest;
        try {
          const existingAppointment = await Appointment.findOne({
            admissionRequestId: requestId,
          });

          if (existingAppointment) {
            logger.info(
              `[homeVisitAppointmnetScheduleSecondReminder] - Home visit appointment already exist for clientId ${requestId}`
            );
            return;
          }
          await sendHomeVisitAppointmentScheduleLinkReminder({
            phone,
            name: firstName,
            type: 'appointmentSchedulerReminder',
            requestId: requestId,
          });

          await sendHomeVisitAppointmentScheduleLinkEmailReminder({
            name: firstName,
            email: email,
            requestId: requestId,
            customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit`,
          });
        } catch (error: any) {
          logger.error(
            `[homeVisitAppointmnetScheduleSecondReminder] - Home visit appointment scheduling link failed to send to clientId ${requestId},`,
            {
              message: error.message,
              stack: error.stack,
            }
          );
        }
      })
    );
  } catch (error: any) {
    logger.error(
      `[homeVisitAppointmnetScheduleSecondReminder] - Error occured while sending home visit appointment scheduling link to clients`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const homeVisitAppointmnetScheduleThirdReminder = async () => {
  try {
    const fiveDaysAgoHomeVisitRequests = await getHomeVisitScheduleLinkSendFiveDaysAgo();

    logger.info(
      `[homeVisitAppointmnetScheduleThirdReminder]: Sending home visit appointment scheduling links to ${fiveDaysAgoHomeVisitRequests.length} clients with applications approved five days ago.`
    );

    await Promise.all(
      fiveDaysAgoHomeVisitRequests.map(async (admissionRequest) => {
        const {
          _id: requestId,
          personalData: { firstName, phone, email },
        } = admissionRequest;

        try {
          const existingAppointment = await Appointment.findOne({
            admissionRequestId: requestId,
          });
          if (existingAppointment) {
            logger.info(
              `[homeVisitAppointmnetScheduleThirdReminder] - Home visit appointment already exist for clientId ${requestId}`
            );
            return;
          }
          await sendHomeVisitAppointmentScheduleLinkReminder({
            phone,
            name: firstName,
            type: 'appointmentSchedulerReminder',
            requestId: requestId,
          });

          await sendHomeVisitAppointmentScheduleLinkEmailReminder({
            name: firstName,
            email: email,
            requestId: requestId,
            customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit`,
          });
        } catch (error: any) {
          logger.error(
            `[homeVisitAppointmnetScheduleThirdReminder] - Home visit appointment scheduling link failed to send to clientId ${requestId},`,
            {
              message: error.message,
              stack: error.stack,
            }
          );
        }
      })
    );
  } catch (error: any) {
    logger.error(
      `[homeVisitAppointmnetScheduleThirdReminder] - Error occured while sending home visit appointment scheduling link to clients`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentScheduling = async () => {
  if (!RUN_HOME_VISITORS_APPOINTMENT_SCHEDULING_REMINDER_CRON) {
    logger.info(`[notifyCustomerAboutHomeVisitAppointmentScheduling] - cron job is disabled`);
    return;
  }
  await homeVisitAppointmnetScheduleFirstReminder();
  await homeVisitAppointmnetScheduleSecondReminder();
  await homeVisitAppointmnetScheduleThirdReminder();
};

export const addAvalDataToAdmissionRequest = async (
  requestId: string,
  requestAvalData: RequestAvalData,
  authUser: AuthUser | undefined
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const saved = await repoAddRequestAvalData(currentAdmissionRequest.id!, requestAvalData);
  logger.info(`[addAvalDataToAdmissionRequest] - aval data added for request ${requestId}`);

  const existingAvalData = currentAdmissionRequest.avalData;
  if (authUser) {
    const oldValues: Record<string, any> = {};
    const updateFields: Record<string, any> = {};

    Object.keys(requestAvalData).forEach((key) => {
      const typedKey = key as keyof typeof requestAvalData;
      if (requestAvalData[typedKey] !== undefined) {
        if (
          existingAvalData &&
          JSON.stringify(requestAvalData[typedKey]) !== JSON.stringify(existingAvalData[typedKey])
        ) {
          updateFields[key] = requestAvalData[typedKey];
          oldValues[key] = existingAvalData[typedKey];
        } else if (!existingAvalData) {
          updateFields[key] = requestAvalData[typedKey];
        }
      }
    });

    await auditTrail.logCustomEvent({
      userId: authUser?.userId,
      entityId: requestId,
      entityType: EntityType.admission_request,
      actionType: ActionType['aval_data.updated'],
      message: 'Datos del deudor solidario cargados',
      metadata: {
        userEmail: authUser?.email,
        oldValue: oldValues,
        newValue: updateFields,
      },
    });
  }

  return saved;
};

export const notifyCustomerAboutHomeVisitAppointmentNoShow = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { firstName, phone },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentNoShowMessage({
      name: firstName!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentNoShow',
    });

    await sendHomeVisitAppointmentNoShowMessageEmail({
      name: firstName!,
      email: currentAdmissionRequest.personalData.email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentNoShow] - Home visit appointment finish message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const addLocationDataToAdmissionRequest = async (
  requestId: string,
  requestLocationData: RequestLocationData
): Promise<AdmissionRequest> => {
  logger.info(`[addLocationDataToAdmissionRequest] - adding location data`);
  const saved = await repoAddLocationData(requestId, requestLocationData);
  logger.info(`[addLocationDataToAdmissionRequest] - location data added`);
  return saved;
};

export const notifyCustomerAboutHomeVisitAppointmentApology = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { firstName, phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentApologyMessage({
      name: firstName!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentApology',
    });

    await sendHomeVisitAppointmentApologyEmail({
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentApology] - Home visit appointment finish message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentCancel = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentCancelMessage({
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentCancel',
    });

    const rescheduleAppointmentLink = `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`;

    await sendHomeVisitAppointmentCancelEmail({
      email: email!,
      customerWebAppLink: rescheduleAppointmentLink,
      requestId: requestId,
    });

    await sendHomeVisitAppointmentCancelFCMNotification({
      userId: requestId,
      rescheduleAppointmentLink,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentCancel] - Home visit appointment cancel message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const googleReverseGeocoding = async (latitude: number, longitude: number) => {
  try {
    const locationDetails = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GEOCODING_API_KEY}`
    );
    if (locationDetails.status === 200 && locationDetails.data.results?.length > 0) {
      return locationDetails.data.results[0].formatted_address as string;
    }
  } catch (error) {
    logger.error(
      `[googleReverseGeocoding] - Unable to get location from co-ordinates lat=${latitude} and lng=${longitude} due to ${error}`
    );
  }
  return null;
};

export const lookupIPAddress = async (ipAddress: string) => {
  try {
    const response = await axios.get<RequestIpInfoData>(`https://ipinfo.io/${ipAddress}`, {
      params: {
        token: IPINFO_TOKEN,
      },
    });
    if (response.status === 200) {
      return response.data;
    }
  } catch (error) {
    logger.error(`[lookupIPAddress] - unable to lookup IP address ${ipAddress} due to ${error}`);
  }
  return null;
};

export const sendHilosLocationGatheringMessage = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);
    const {
      personalData: { firstName, phone, email },
    } = currentAdmissionRequest;
    await sendLocationGatheringMessageToUser({
      name: firstName!,
      phone: phone!,
      requestId: requestId,
      type: 'locationGatheringMessage',
    });

    await sendHomeVisitLocationVerificationEmail({
      name: firstName!,
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/user-info-gathering`,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[sendHilosLocationGatheringMessage] - Location gathering message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const sendHomeImageUploadNotificationMessage = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);
    const {
      personalData: { firstName, phone, email },
    } = currentAdmissionRequest;
    await sendHomeImageUploadMessageToUser({
      name: firstName!,
      phone: phone!,
      requestId: requestId,
      type: 'homeImageUploadMessage',
    });

    await sendHomeImageUploadEmail({
      name: firstName!,
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/home-image-upload`,
      requestId: requestId,
    });
  } catch (error: any) {
    logger.error(
      `[sendHilosHomeImageUploadMessage] - Home Image upload message failed to send to clientId ${requestId}.`,
      {
        message: error?.message,
        stack: error?.stack,
      }
    );
  }
};

export const resetHomeVisitScheduleLinkSendDateToToday = async (requestId: string, user: AuthUser) => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);
  const previousHomeVisitScheduleLinkSendDate = current.homeVisitScheduleLinkSendDate;

  await repoUpdateHomeVisitAppointmentSchedulingLinkSendDate(current.id!, new Date());
  logger.info(
    `[updateHomeVisitScheduleLinkSendDate] - home visit schedule link send date updated for ${requestId}`
  );

  await auditTrail.logCustomEvent({
    userId: user.userId,
    entityId: requestId,
    entityType: EntityType.admission_request,
    actionType: ActionType['admission_request.homeVisitScheduleLinkSendDate.reset'],
    message: `Fecha de envío del enlace de programación de la visita domiciliaria restablecida, fecha anterior: ${previousHomeVisitScheduleLinkSendDate}`,
    metadata: {
      userEmail: user.email,
    },
  });
};
