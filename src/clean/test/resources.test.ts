import request from 'supertest';
import app from '../../app'; // Assuming you have an Express app instance in app.ts
import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';
import { getOcnUsername } from '../../services/onboarding/getOCNUser';
import { sendEmailToUSCustomerAfterRegistering } from '../../modules/platform_connections/emailFunc';
import axios from 'axios';
import {
  addAvalDataToAdmissionRequest,
  addLocationDataToAdmissionRequest,
  googleReverseGeocoding,
  lookupIPAddress,
} from '../domain/usecases';
import {
  AdmissionRequest,
  DailyEarning,
  EarningsAnalysis,
  PalencaAccount,
  PalencaAccountRetrieval,
  RequestAvalData,
  RequestDocument,
  RequestDocumentsAnalysis,
  RequestLocationData,
  RequestPalenca,
  RequestPersonalData,
  RiskAnalysis,
  WeeklyEarning,
} from '../domain/entities';
import {
  AdmissionRequestAdditionalDocumentType,
  AdmissionRequestDocumentType,
  AdmissionRequestStatus,
  CurrencyCode,
  EarningsAnalysisStatus,
  GigPlatform,
  PalencaAccountStatus,
  PalencaRetrievalStatus,
  RequestDocumentsAnalysisStatus,
  RequestDocumentStatus,
  RequestPersonalDataStepStatus,
  AnalysisStatus,
  ScorecardVersion,
} from '../domain/enums';

jest.mock('../../models/admissionRequestSchema');
jest.mock('../../services/onboarding/getOCNUser');
jest.mock('../../modules/platform_connections/emailFunc');
jest.mock('axios');
jest.mock('../domain/usecases');

describe('createAdmissionRequestResource', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a new admission request successfully', async () => {
    const mockUserData = { _id: 'userId' };
    const mockHubspotResponse = { data: { id: 'hubspotId' } };
    const mockHilosResponse = { data: { id: 'hilosId' } };

    (getOcnUsername as jest.Mock).mockResolvedValue(mockUserData);
    (AdmissionRequestMongo.findOne as jest.Mock).mockResolvedValue(null);
    (axios.post as jest.Mock).mockResolvedValueOnce(mockHubspotResponse);
    (axios.post as jest.Mock).mockResolvedValueOnce(mockHilosResponse);
    (AdmissionRequestMongo.updateOne as jest.Mock).mockResolvedValue(null);
    (sendEmailToUSCustomerAfterRegistering as jest.Mock).mockResolvedValue(null);

    const response = await request(app).post('/admission-request').send({
      firstName: 'John',
      lastName: 'Doe',
      phone: '**********',
      email: '<EMAIL>',
      vehicleType: 'car',
      city: 'CDMX/EDOMEX',
      country: 'mx',
      vehicleSelected: 'sedan',
      state: 'state',
      postalCode: '12345',
      source: 'source',
      clientIpAddress: '127.0.0.1',
    });

    expect(response.status).toBe(200);
    expect(response.body.data.hubspot).toEqual(mockHubspotResponse.data);
    expect(response.body.data.hilos).toEqual(mockHilosResponse.data);
  });

  it('should return 409 if client with email or phone already exists', async () => {
    const mockExistRequest = { _id: 'existingRequestId', hubspot: {}, hilos: {} };

    (AdmissionRequestMongo.findOne as jest.Mock).mockResolvedValue(mockExistRequest);

    const response = await request(app).post('/admission-request').send({
      firstName: 'John',
      lastName: 'Doe',
      phone: '**********',
      email: '<EMAIL>',
      vehicleType: 'car',
      city: 'CDMX/EDOMEX',
      country: 'mx',
      vehicleSelected: 'sedan',
      state: 'state',
      postalCode: '12345',
      source: 'source',
      clientIpAddress: '127.0.0.1',
    });

    expect(response.status).toBe(409);
    expect(response.body.message).toBe('Client with email already exists');
  });

  it('should return 500 if there is an error during the process', async () => {
    (getOcnUsername as jest.Mock).mockRejectedValue(new Error('Error'));

    const response = await request(app).post('/admission-request').send({
      firstName: 'John',
      lastName: 'Doe',
      phone: '**********',
      email: '<EMAIL>',
      vehicleType: 'car',
      city: 'CDMX/EDOMEX',
      country: 'mx',
      vehicleSelected: 'sedan',
      state: 'state',
      postalCode: '12345',
      source: 'source',
      clientIpAddress: '127.0.0.1',
    });

    expect(response.status).toBe(500);
    expect(response.body.message).toBe('Error al enviar solicitud');
  });
});

const requestId = '67ae02f6799ebd5b24f6a893';
const mockAdmissionRequestResponse: AdmissionRequest = {
  id: requestId,
  status: AdmissionRequestStatus.documents_analysis,
  rejectionReason: undefined,
  personalData: new RequestPersonalData({
    firstName: 'KhanTest',
    lastName: 'Test',
    phone: '+527213102321',
    email: '<EMAIL>',
    status: RequestPersonalDataStepStatus.completed,
    birthdate: '1685-05-13',
    taxId: 'PEGI850315HDA',
    nationalId: 'VBCM860801MDFRNN04',
    postalCode: '12345',
    country: 'mx',
    city: 'CDMX/EDOMEX',
    state: 'Aguascalientes',
    neighborhood: 'Mexico',
    street: 'Mexico',
    streetNumber: '12345',
    department: null,
    ssn: null,
    rideShareTotalRides: null,
    avgEarningPerWeek: null,
    termsAndConditions: null,
    dataPrivacyConsentForm: null,
    nationality: null,
    age: null,
    occupation: null,
    homePhone: null,
    timeInResidency: null,
    municipality: null,
    maritalStatus: 'Single',
    dependents: null,
    spouseOrPartnerIncome: null,
    partnerSourceOfIncome: null,
    partnerName: null,
    partnerPhone: null,
    noOfDependents: null,
    dependendsInfo: null,
    ownACar: null,
    carLeasingtime: null,
    carMake: null,
    carModel: null,
    ownDebt: null,
    outStandingDebt: null,
    doesDebtAffectPersonalFinance: null,
    references: null,
    learnAboutOcn: null,
    referrer: null,
  }),
  documentsAnalysis: new RequestDocumentsAnalysis({
    status: RequestDocumentsAnalysisStatus.pending,
    documents: [
      new RequestDocument({
        mediaId: '67ae0373eea4fafe25cdb6a8',
        status: RequestDocumentStatus.pending_review,
        type: AdmissionRequestDocumentType.identity_card_front,
      }),
      new RequestDocument({
        mediaId: '67ae0378eea4fafe25cdb74b',
        status: RequestDocumentStatus.pending_review,
        type: AdmissionRequestDocumentType.identity_card_back,
      }),
      new RequestDocument({
        mediaId: '67ae037deea4fafe25cdb7ef',
        status: RequestDocumentStatus.pending_review,
        type: AdmissionRequestDocumentType.proof_of_address,
      }),
      new RequestDocument({
        mediaId: '67ae0383eea4fafe25cdb894',
        status: RequestDocumentStatus.pending_review,
        type: AdmissionRequestDocumentType.bank_statement_month_1,
      }),
      new RequestDocument({
        mediaId: '67ae0386eea4fafe25cdb897',
        status: RequestDocumentStatus.pending_review,
        type: AdmissionRequestDocumentType.bank_statement_month_2,
      }),
      new RequestDocument({
        mediaId: '67ae0389eea4fafe25cdb89a',
        status: RequestDocumentStatus.pending_review,
        type: AdmissionRequestDocumentType.bank_statement_month_3,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.proof_of_tax_situation,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.drivers_license_front,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.drivers_license_back,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.selfie_photo,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.garage_photo,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back,
      }),
      new RequestDocument({
        mediaId: null,
        status: RequestDocumentStatus.pending,
        type: AdmissionRequestAdditionalDocumentType.curp,
      }),
    ] as RequestDocument[],
  }),
  palenca: new RequestPalenca({
    widgetId: '04d4f458-9991-4c44-9464-8786cb9e046e',
    externalId: '67ae02f6799ebd5b24f6a892',
    accounts: [
      new PalencaAccount({
        accountId: '*************',
        platform: GigPlatform.didi,
        status: PalencaAccountStatus.success,
        earnings: new PalencaAccountRetrieval({
          status: PalencaRetrievalStatus.success,
        }),
        metrics: new PalencaAccountRetrieval({
          status: PalencaRetrievalStatus.success,
        }),
        createdAt: new Date(),
      }),
    ],
  }),
  earningsAnalysis: new EarningsAnalysis({
    status: EarningsAnalysisStatus.approved,
    totalEarnings: 84000,
    earnings: Array<WeeklyEarning>(11).fill(
      new WeeklyEarning({
        totalAmount: 7000,
        totalTrips: 0,
        fromDate: new Date(),
        toDate: new Date(),
        week: 6,
        year: 2025,
        currency: CurrencyCode.mxn,
        dailyEarnings: [
          new DailyEarning({
            amount: 7000,
            countTrips: 0,
            earningDate: new Date(),
            currency: CurrencyCode.mxn,
          }),
        ] as DailyEarning[],
      })
    ),
    platforms: 1,
  }),
  riskAnalysis: new RiskAnalysis({
    status: AnalysisStatus.pending,
    scorecardVersion: ScorecardVersion.v1,
  }),
  avalData: new RequestAvalData({
    name: 'Khan',
    phone: '**********',
    email: '<EMAIL>',
    location: 'Mexico',
  }),
};

describe('addAvalDataResource', () => {
  it('should successfully add data and return 200 - [AdmissionRequest]', async () => {
    (addAvalDataToAdmissionRequest as jest.Mock).mockResolvedValue(mockAdmissionRequestResponse);

    const response = await request(app).post(`/public/admission/requests/${requestId}/aval-data`).send({
      name: 'Khan',
      phone: '**********',
      email: '<EMAIL>',
      location: 'Mexico',
    });

    expect(response.status).toBe(200);
    expect(response.body.data.avalData).not.toBeNull();
    expect(response.body.data.avalData).toEqual({
      name: 'Khan',
      phone: '**********',
      email: '<EMAIL>',
      location: 'Mexico',
    });
  });

  it('should handle validation errors', async () => {
    const error = new Error('unprocessable_entity');

    (addAvalDataToAdmissionRequest as jest.Mock).mockImplementation(() => {
      throw error;
    });

    const response = await request(app).post(`/public/admission/requests/${requestId}/aval-data`);

    expect(response.status).toEqual(400);
    expect(response.body.error).toBeDefined();
    expect(response.body.error.code).toEqual('unprocessable_entity');
  });
});

describe('latLongLookup', () => {
  it('should return 200 and serialized location data for valid latitude and longitude', async () => {
    const updatedMockAdmissionRequestResponse = {
      ...mockAdmissionRequestResponse,
      locationData: new RequestLocationData({
        latitude: 40.712776,
        longitude: -74.005974,
        location: 'mexico',
        ipAddress: '************',
        isFromIP: false,
      }),
    };
    (googleReverseGeocoding as jest.Mock).mockReturnValue('mexico');
    (addLocationDataToAdmissionRequest as jest.Mock).mockResolvedValue(updatedMockAdmissionRequestResponse);

    const response = await request(app).post(`/public/admission/requests/${requestId}/lat-long-lookup`).send({
      latitude: 40.712776,
      longitude: -74.005974,
      ipAddress: '************',
    });

    expect(response.status).toBe(200);
    expect(response.body.data).not.toBeNull();
    expect(response.body.data.locationData).toEqual({
      latitude: 40.712776,
      longitude: -74.005974,
      location: 'mexico',
      ipAddress: '************',
      isFromIP: false,
    });
  });

  it('should return 400 for invalid latitude and longitude', async () => {
    (googleReverseGeocoding as jest.Mock).mockReturnValue(null);
    const response = await request(app).post(`/public/admission/requests/${requestId}/lat-long-lookup`).send({
      latitude: 200,
      longitude: 500,
      ipAddress: '************',
    });
    expect(response.status).toBe(400);
  });

  it('should return 400 for empty or null IP address', async () => {
    const updatedMockAdmissionRequestResponse = {
      ...mockAdmissionRequestResponse,
      locationData: new RequestLocationData({
        latitude: 40.712776,
        longitude: -74.005974,
        location: 'mexico',
        ipAddress: '************',
        isFromIP: false,
      }),
    };
    (googleReverseGeocoding as jest.Mock).mockReturnValue('mexico');
    (addLocationDataToAdmissionRequest as jest.Mock).mockResolvedValue(updatedMockAdmissionRequestResponse);
    const response = await request(app).post(`/public/admission/requests/${requestId}/lat-long-lookup`).send({
      latitude: 40.712776,
      longitude: -74.005974,
    });
    expect(response.status).toBe(400);
  });

  it('should return 400 for empty or null longitude', async () => {
    (googleReverseGeocoding as jest.Mock).mockReturnValue('mexico');
    const response = await request(app).post(`/public/admission/requests/${requestId}/lat-long-lookup`).send({
      latitude: 40.712776,
      ipAddress: '************',
    });
    expect(response.status).toBe(400);
  });

  it('should return 400 for empty or null latitude', async () => {
    (googleReverseGeocoding as jest.Mock).mockReturnValue('mexico');
    const response = await request(app).post(`/public/admission/requests/${requestId}/lat-long-lookup`).send({
      longitude: -74.005974,
      ipAddress: '************',
    });
    expect(response.status).toBe(400);
  });
});

describe('ipLookup', () => {
  it('should return 200 and serialized location data for valid IP address', async () => {
    const updatedMockAdmissionRequestResponse = {
      ...mockAdmissionRequestResponse,
      locationData: new RequestLocationData({
        latitude: 42.1015,
        longitude: -72.5898,
        location: 'Massachusetts',
        ipAddress: '************',
        isFromIP: true,
      }),
    };
    (lookupIPAddress as jest.Mock).mockReturnValue({
      ip: '************',
      hostname: 'ip-66-87-125-72.spfdma.spcsdns.net',
      city: 'Springfield',
      region: 'Massachusetts',
      country: 'US',
      loc: '42.1015,-72.5898',
      org: 'AS10507 Sprint Personal Communications Systems',
      postal: '01101',
      timezone: 'America/New_York',
    });
    (addLocationDataToAdmissionRequest as jest.Mock).mockResolvedValue(updatedMockAdmissionRequestResponse);

    const response = await request(app).post(`/public/admission/requests/${requestId}/ip-lookup`).send({
      ipAddress: '************',
    });

    expect(response.status).toBe(200);
    expect(response.body.data).not.toBeNull();
    expect(response.body.data.locationData).toEqual({
      latitude: 42.1015,
      longitude: -72.5898,
      location: 'Massachusetts',
      ipAddress: '************',
      isFromIP: true,
    });
  });

  it('should return 400 if IP address is empty or null', async () => {
    const response = await request(app).post(`/public/admission/requests/${requestId}/ip-lookup`).send();

    expect(response.status).toBe(400);
  });
});
