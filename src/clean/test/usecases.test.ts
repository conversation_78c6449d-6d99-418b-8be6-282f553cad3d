// Import necessary dependencies
import User, { UserMongoI } from '@/models/userSchema';

// Mock the User model
jest.mock('@/models/userSchema');

describe('getHomeVisitor', () => {
  beforeEach(() => {
    // Clear mocks before each test case
    jest.clearAllMocks();
  });

  it('should return the visitor with the smallest homeVisitCount', async () => {
    // Simulate database response
    const mockVisitors = [
      { homeVisitCount: 5, appointmentSchedulerPageLink: 'link1' },
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link2' },
      { homeVisitCount: 8, appointmentSchedulerPageLink: 'link3' },
    ];

    const sortedMockVisitors = [
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link2' },
      { homeVisitCount: 5, appointmentSchedulerPageLink: 'link1' },
      { homeVisitCount: 8, appointmentSchedulerPageLink: 'link3' },
    ];

    // Mock the User.find() method to return mockVisitors
    (User.find as jest.Mock).mockReturnValue({
      lean: jest.fn().mockResolvedValue(mockVisitors),
      sort: jest.fn().mockReturnValue(sortedMockVisitors),
    });
  });

  it('should return the only visitor when there is one visitor', async () => {
    const mockVisitors = [{ homeVisitCount: 10, appointmentSchedulerPageLink: 'link1' }];

    // Mock the User.find() method to return mockVisitors
    (User.find as jest.Mock).mockReturnValue({
      lean: jest.fn().mockResolvedValue(mockVisitors),
      sort: jest.fn().mockReturnValue(mockVisitors),
    });
  });

  it('should throw an error when User.find fails', async () => {
    // Mock the User.find method to simulate an error
    (User.find as jest.Mock).mockReturnValue({
      lean: jest.fn().mockResolvedValue([]),
      sort: jest.fn().mockReturnValue([]),
    });
  });

  it('should handle empty homeVisitCount gracefully', async () => {
    const mockVisitors = [
      { homeVisitCount: 0, appointmentSchedulerPageLink: 'link1' },
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link2' },
    ];

    // Mock the User.find() method to return mockVisitors
    (User.find as jest.Mock).mockReturnValue({
      lean: jest.fn().mockResolvedValue(mockVisitors),
      sort: jest.fn().mockReturnValue(mockVisitors),
    });
  });

  it('should return the visitor with the smallest homeVisitCount even if counts are equal', async () => {
    const mockVisitors = [
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link1' },
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link2' },
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link3' },
    ];

    // Mock the User.find() method to return mockVisitors
    (User.find as jest.Mock).mockReturnValue({
      lean: jest.fn().mockResolvedValue(mockVisitors),
      sort: jest.fn().mockReturnValue(mockVisitors),
    });
  });

  it('should handle visitors with empty or invalid appointmentSchedulerPageLink gracefully', async () => {
    const mockVisitors = [
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link1' },
      { homeVisitCount: 2, appointmentSchedulerPageLink: '' }, // Invalid link
      { homeVisitCount: 1, appointmentSchedulerPageLink: 'link2' },
    ];

    const sortedMockVisitors = [
      { homeVisitCount: 1, appointmentSchedulerPageLink: 'link2' },
      { homeVisitCount: 2, appointmentSchedulerPageLink: '' }, // Invalid link
      { homeVisitCount: 3, appointmentSchedulerPageLink: 'link1' },
    ];

    // Mock the User.find() method to return mockVisitors
    (User.find as jest.Mock).mockReturnValue({
      lean: jest.fn().mockResolvedValue(mockVisitors),
      sort: jest.fn().mockReturnValue(sortedMockVisitors),
    });
  });
});

describe('updateHomeVisitorCount', () => {
  const user: Partial<UserMongoI> = {
    id: '67442f0e781a7c9d0fe81729',
    email: '<EMAIL>',
    password: 'abc-test',
    name: 'Faiq',
    city: 'mexico',
    isVerified: true,
    role: 'home-visitor',
    settings: {
      allowedRegions: [],
    },
    googleSignfailureCount: 0,
  };

  beforeEach(() => {
    // Clear mocks before each test case
    jest.clearAllMocks();
  });

  it('should update the homeVisitCount correctly', async () => {
    // Mock the User.updateOne method to resolve successfully
    (User.updateOne as jest.Mock).mockResolvedValue({ nModified: 1 }); // Simulate successful update
  });

  it('should handle failure when updateOne throws an error', async () => {
    // Mock the User.updateOne method to simulate an error
    const errorMessage = 'Database update failed';
    (User.updateOne as jest.Mock).mockRejectedValue(new Error(errorMessage)); // Simulate an error
  });

  it('should not call updateOne if the count is the same', async () => {
    // Mock the User.updateOne method to resolve successfully
    (User.updateOne as jest.Mock).mockResolvedValue({ nModified: 0 }); // No update occurred
    // Assert that User.updateOne was NOT called if the count is the same
    expect(User.updateOne).toHaveBeenCalledTimes(1);
    expect(User.updateOne).toHaveBeenCalledWith({ _id: user.id }, { $inc: { homeVisitCount: 1 } });
  });
});
