/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import cron from 'node-cron';
import { RUN_FETCH_PALENCA_ACCOUNTS_CRON, RUN_FETCH_VEHICLE_VIOLATION_CRON } from './constants';
import {
  notificationEmailForCustomersWhoHasNotStartedOnboardingWithInTwoDays,
  markAdmissionRequestAsLostAndNoActivity,
} from './clean/domain/usecases';
import { calendarService } from './modules/Calendar/services/calendar.service';
import { vehicleViolationService } from './modules/VehicleViolation/services/vehicleViolation.service';
import { logger } from './clean/lib/logger';
import { fcmNotificationService } from './modules/FirebaseCloudMessaging/services/fcmNotification.service';
import { processUnprocessedPalencaProfiles } from './services/palenca';

const initScheduleJobs = () => {
  const autoNotificationEmailForCustomers = cron.schedule(
    '0 0 * * *', // run every day at 00:00
    async () => {
      await notificationEmailForCustomersWhoHasNotStartedOnboardingWithInTwoDays();
    },
    { timezone: 'America/Mexico_City' }
  );
  autoNotificationEmailForCustomers.start();

  /**
   * this cron will run from monday-friday every night at 10PM to notify customers about their appointment the next day
   */
  const autoNotificationForCustomersAboutHomeVisitAppointmentOneNightAgo = cron.schedule(
    '0 22 * * 1-5', //
    async () => {
      await calendarService.notifyCustomersAboutTheirAppointmentOneNightAgoCron();
    },
    { timezone: 'America/Mexico_City' }
  );

  /**
   * this cron will run every monday-friday from 8:30AM till 5:30PM every half hour
   */
  const autoNotificationForCustomersAboutHomeVisitAppointmentHalfHourAgo = cron.schedule(
    '*/10 8-17 * * 1-5',
    async () => {
      await calendarService.notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore();
    },
    { timezone: 'America/Mexico_City' }
  );

  /**
   *  this cron will run every sunday at 8PM to add slots for home visitors for the next two weeks
   */
  const slotsOfNextTwoWeeksForHomeVisitors = cron.schedule(
    '0 17 * * 7',
    async () => {
      await calendarService.addSlotsOfNextTwoWeeksForHomeVisitosCron();
    },
    { timezone: 'America/Mexico_City' }
  );

  autoNotificationForCustomersAboutHomeVisitAppointmentOneNightAgo.start();
  autoNotificationForCustomersAboutHomeVisitAppointmentHalfHourAgo.start();
  slotsOfNextTwoWeeksForHomeVisitors.start();

  if (!RUN_FETCH_VEHICLE_VIOLATION_CRON) {
    logger.info(`[VehicleViolation] - fetchVehicleViolation cron job is disabled`);
  } else {
    vehicleViolationService.runRandomizedViolationFetchCDMX();
  }

  if (!RUN_FETCH_PALENCA_ACCOUNTS_CRON) {
    logger.info(`[PalencaService] - processUnprocessedPalencaProfiles cron job is disabled`);
  } else {
    /**
     *  this cron will run every 2 mins
     */
    const processUnprocessedPalencaProfilesCron = cron.schedule(
      '*/2 * * * *',
      async () => {
        await processUnprocessedPalencaProfiles();
      },
      { timezone: 'America/Mexico_City' }
    );
    processUnprocessedPalencaProfilesCron.start();
  }

  /**
   * this cron will run every day at 9AM to check if any notification is in failed state
   * then send it again
   */
  const resendFailedNotifications = cron.schedule(
    '0 9 * * *',
    async () => {
      await fcmNotificationService.resendFailedNotifications();
    },
    { timezone: 'America/Mexico_City' }
  );
  resendFailedNotifications.start();

  const markAdmissionRequestAsLostAndNoActivityCron = cron.schedule(
    '0 3 1 * *',
    async () => {
      await markAdmissionRequestAsLostAndNoActivity();
    },
    { timezone: 'America/Mexico_City' }
  );
  markAdmissionRequestAsLostAndNoActivityCron.start();
};

export default initScheduleJobs;
