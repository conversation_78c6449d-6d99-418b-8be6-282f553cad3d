import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import vehicleRoutes from './routes/vehicles';
import auth from './routes/auth';
import user from './routes/user';
import associate from './routes/associate';
import candidate from './routes/send-cv';
import stock from './routes/stockVehicles';
import { verifyToken } from './middlewares/verifyToken';
import { catchExceptionsMiddleWare } from './middlewares/error';
import { upload } from './multer/multer';
import {
  gigstackWebhookCdmx,
  gigstackWebhookGdl,
  gigstackWebhookMoka,
  gigstackWebhookMty,
} from './controllers/gigstack';
import hubspot from './routes/hubspot';
import contract from './routes/contract';
import { displayText, isDev, isProd } from './constants';
import gighooks from './routes/gighooks';
import stp from './routes/stp';
import wire4 from './routes/wire4';
import gps from './routes/gps';
import ocnPayments from './routes/ocnpayments';
import ocr from './routes/ocr';
import platforms, { platformUrl } from './modules/platform_connections/routes';
import { cleanRouter } from './clean/presentation/routes';
// import { logRequestAndResponseMiddleware } from './middlewares/logger';
import mainContractRoutes from './modules/MainContract/routes/mainContractRoutes';
import associatePaymentsRoute from './modules/AssociatePayments/routes/associatePaymentRoutes';
import cronJobTempPayments from './modules/TempSuscriptionPayments/routes';
import weeklyRecordsMain from './modules/WeeklyRecords/routes';
import associateRouter from './modules/Associate/routes/associate.route';
import webhooks from './modules/Webhooks/index.routes';
import admissionReques from './routes/admissionRequest';
import onboarding from './routes/onboarding';
import mlservice from './modules/AI/routes/ml.route';
import aiservice from './modules/AI/routes/ai.route';
import adminCreateVendorsRouter from './modules/CreateVendors';
import vendorPlatformMainRouter from './vendor-platform/index.routes';
import calendarRouter from './modules/Calendar/routes/calendar.routes';
import carRouter from './routes/car';
import customerAcknowledgementRouter from './routes/customerAcknowledgement';
import insuranceRouter from './routes/insurance';
import { getVehicleRedirectInfo } from './controllers/stockVehicles';
import leadAssignment from './routes/leadAssignment';
import gestoresRouter from './routes/gestores';
import vehicleViolationRouter from './modules/VehicleViolation/routes/vehicleViolation.routes';
import fcmNotificationRouter from './modules/FirebaseCloudMessaging/routes/fcmNotification.routes';
import requestNotesRouter from './routes/requestNotes';
import permissionSet from './routes/permissionSet';
import palencaRouter from './routes/palenca';
import vehicleInspectionRouter from './routes/vehicleInspection';
import verificentros from './routes/vereficentros';

const app = express();
app.use(express.json());
app.use(cors());

if (isDev || isProd) {
  app.use(
    morgan((tokens, req, res) => {
      const log = {
        timestamp: new Date().toISOString(),
        method: tokens.method(req, res),
        url: tokens.url(req, res),
        status: tokens.status(req, res),
        response_time: `${tokens['response-time'](req, res)} ms`,
        user_agent: tokens['user-agent'](req, res),
        level: 'info',
      };
      if (!log.status) {
        return JSON.stringify(log);
      }
      if (Number(log.status) > 400 && Number(log.status) < 500) {
        log.level = 'warn';
      }
      if (Number(log.status) === 400 || Number(log.status) >= 500) {
        log.level = 'error';
      }
      return JSON.stringify(log);
    })
  );
} else {
  app.use(morgan('dev'));
}

app.get('/', (_req, res) => {
  res.status(200).send(displayText.home.title);
});

app.use(platformUrl, platforms);
// app.use(logRequestAndResponseMiddleware);

app.use(adminCreateVendorsRouter);
app.use(vendorPlatformMainRouter);

// Rasayel-Gigstack webhooks
app.post('/gigstack/cdmx', gigstackWebhookCdmx);
app.post('/gigstack/gdl', gigstackWebhookGdl);
app.post('/gigstack/mty', gigstackWebhookMty);
app.post('/gigstack/moka', gigstackWebhookMoka);
app.get('/stock-vehicle/:vehicleId/redirect-info', getVehicleRedirectInfo);
app.use('/gighooks', gighooks);
app.use('/ocnpayments', ocnPayments);

// routes

app.use(cronJobTempPayments);
app.use('/hubspot', hubspot);
app.use('/auth', auth);
app.use('/candidate', upload.single('cv'), candidate);
app.use('/vehicles', vehicleRoutes);
app.use('/stp', stp);
app.use('/wire4', wire4);
app.use('/ocr', ocr);
app.use('/webhooks', webhooks);
app.use('/palenca', palencaRouter);
app.use('/onboarding', onboarding);
app.use('/mlservice', mlservice);
app.use('/aiservice', aiservice);
app.use('/admissionRequest', admissionReques);
app.use('/verificentros', verificentros);
app.use(cleanRouter);
app.use(weeklyRecordsMain);
app.use(verifyToken);
app.use('/customer', customerAcknowledgementRouter);
app.use(calendarRouter);
app.use('/vehicleViolation', vehicleViolationRouter);
app.use('/car', carRouter);
app.use('/gps', gps);
app.use('/stock', stock);
app.use('/leadAssignment', leadAssignment);
app.use('/associate', associate, associateRouter);
app.use('/user', user);
app.use('/contract', contract);
app.use('/associatePayments', associatePaymentsRoute);
app.use('/mainContract', mainContractRoutes);
app.use('/insurance', insuranceRouter);
app.use('/gestores', gestoresRouter);
app.use('/notification', fcmNotificationRouter);
app.use('/notes', requestNotesRouter);
app.use('/vehicle-inspection', vehicleInspectionRouter);

// Error handling
app.use(catchExceptionsMiddleWare);
app.use('/permissionSet', permissionSet);

export default app;
