import { AsyncController } from '../types&interfaces/types';
import { logger } from '../clean/lib/logger';
import {
  getVehicleInspectionService,
  createOrUpdateInspectionService,
  getAllVehicleInspectionsService,
} from '../services/vehicleInspectionService';

// Create vehicle inspection with images (always creates new record for history)
export const createOrUpdateInspection: AsyncController = async (req, res) => {
  try {
    const {
      vehicleId,
      status,
      issueDescription,
      exterior360Photos,
      interiorPhotos,
      odometerPhotos,
      enginePhotos,
      toolsPhotos,
      chargerPhotos,
      spareTirePhotos,
      batteryPhotos,
      issuePhotos,
    } = req.body;
    console.log('createOrUpdateInspection req.body => ', req.body);

    const inspection = await createOrUpdateInspectionService({
      vehicleId,
      status,
      issueDescription,
      exterior360Photos,
      interiorPhotos,
      odometerPhotos,
      enginePhotos,
      toolsPhotos,
      chargerPhotos,
      spareTirePhotos,
      batteryPhotos,
      issuePhotos,
    });

    return res.status(201).json({
      success: true,
      message: 'Vehicle inspection created successfully.',
      inspection,
    });
  } catch (error) {
    logger.error(`[createOrUpdateInspection] Error: ${error instanceof Error ? error.message : error}`);
    return res.status(500).json({ message: 'Error creating vehicle inspection.' });
  }
};

// Get vehicle inspection by vehicle ID with signed URLs for photos
export const getVehicleInspection: AsyncController = async (req, res) => {
  try {
    const { vehicleId } = req.params;

    const inspection = await getVehicleInspectionService(vehicleId);

    if (!inspection) {
      return res.status(404).json({ message: 'Vehicle inspection not found.' });
    }

    return res.status(200).json({
      success: true,
      message: 'Vehicle inspection retrieved successfully.',
      inspection,
    });
  } catch (error) {
    logger.error(`[getVehicleInspection] Error: ${error instanceof Error ? error.message : error}`);
    return res.status(500).json({ message: 'Error retrieving vehicle inspection.' });
  }
};

// Get all vehicle inspections by vehicle ID
export const getAllVehicleInspections: AsyncController = async (req, res) => {
  try {
    const { vehicleId } = req.params;

    const inspections = await getAllVehicleInspectionsService(vehicleId);

    return res.status(200).json({
      success: true,
      message: 'Vehicle inspections retrieved successfully.',
      inspections,
      count: inspections.length,
    });
  } catch (error) {
    logger.error(`[getAllVehicleInspections] Error: ${error instanceof Error ? error.message : error}`);
    return res.status(500).json({ message: 'Error retrieving vehicle inspections.' });
  }
};
