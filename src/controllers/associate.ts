import { Types } from 'mongoose';
import { AsyncController } from '../types&interfaces/types';
import User from '../models/userSchema';
// import { parsedProperties } from '../services/intex';
import Document from '../models/documentSchema';
import { removeEmptySpacesNameFile } from '../services/removeEmptySpaces';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleStatus,
  VehicleSubCategory,
} from '../models/StockVehicleSchema';
import { getCurrentDateTime } from '../services/timestamps';
import Associate from '../models/associateSchema';
import { deleteFileFromS3, uploadFile } from '../aws/s3';
import {
  associateText,
  CountriesEnum,
  documentsResponse,
  genericMessages,
  SalesFunnelStage,
  steps,
  stockVehiclesText,
  STP_CONSTANTS,
  wire4Data,
} from '../constants';
import { getUserData } from '../services/getUserData';
import { mapReplaceArrayOfDocsId } from '../services/getPropertyWithUrls';
import AssociatePayments from '../models/associatePayments';
import { createWire4BankAccount, createWire4I80BankAccount } from '../services/createWire4BankAccount';
import { createFirstAssociateGigPayment } from '../services/createFirstAssociateGigPayment';
import { createGigUser } from '../services/createGigUser';
import { createGigRecurrentPay } from '../services/createGigRecurrentPay';
import MainContractSchema from '../models/mainContractSchema';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../constants/payments-api';
import axios from 'axios';
import StartPayFlow from '../models/start-pay-flow';
import { revertDate } from '../services/formatDate';
import { associateServiceUS } from '../modules/Associate/services/associate-us.service';
import { associateService } from '../modules/Associate/services/associate.service';
import { AdmissionRequestMongo } from '@/models/admissionRequestSchema';
import { logger } from '@/clean/lib/logger';
import { AdmissionRequestStatus } from '@/constants/socialScoring';
// import { sendConfimClientDataToSQS } from '../services/confirmClientPostToSQS';
import AssociateUS from '@/models/associateSchemaUS';
import { RequestAvalData, RequestDocumentsAnalysis } from '@/clean/domain/entities';
import { Country } from '@/clean/domain/enums';
import { sendNewInstallationNotification } from '@/vendor-platform/modules/company/utils/installationNotifications';
import { updateStageSubStage } from '@/clean/domain/usecases';

export type CreateClient = {
  contractNumber: string;
  name: string;
  lastName: string;
  legal_name: string;
  email: string;
  phone: string;
  rfc: string;
  region: string;
  tax_system: string;
  zip: string;
  associateId: string;
  use_cfdi?: string;
  metadata?: object;
  country?: string;
  state?: string;
  city?: string;
};

interface HistoryDataProps {
  userId: Types.ObjectId;
  step: string;
  description: string;
  time: string;
}

const processDocuments = (
  documentsAnalysis: RequestDocumentsAnalysis,
  country: string,
  avalData?: RequestAvalData
) => {
  const documentTypeToField: { [key: string]: string } = {
    identity_card_front: 'ineFront',
    identity_card_back: 'ineBack',
    proof_of_tax_situation: 'taxStatus',
    proof_of_address: 'addressVerification',
    drivers_license_front: 'driverLicenseFront',
    drivers_license_back: 'driverLicenseBack',
    garage_photo: 'garage',
    selfie_photo: 'picture',
    curp: 'curp',
  };

  const bankStatementMap: Record<string, string> = {
    '1': 'One',
    '2': 'Two',
    '3': 'Three',
    '4': 'Four',
    '5': 'Five',
    '6': 'Six',
  };

  let docs: { [key: string]: Types.ObjectId } = {};
  let bankStatements: { [key: string]: Types.ObjectId } = {};
  let solidarityFrontApproved = true;
  let solidarityBackApproved = true;
  let hasSolidarityFront = true;
  let hasSolidarityBack = true;

  if (country === CountriesEnum.Mexico) {
    solidarityFrontApproved = false;
    solidarityBackApproved = false;
    hasSolidarityFront = false;
    hasSolidarityBack = false;
  }

  let formattedAvalData: {
    name: string | null;
    phone: string | null;
    email: string | null;
    address: string | null;
    ine?: string | null;
  } = {
    name: avalData?.name || null,
    phone: avalData?.phone || null,
    email: avalData?.email || null,
    address: avalData?.location || null,
  };

  documentsAnalysis.documents.forEach(({ type, mediaId, status }) => {
    if (!mediaId) return;

    if (type.startsWith('bank_statement_month_') && status == 'approved') {
      const monthKey = type.slice(-1);
      if (bankStatementMap[monthKey]) {
        bankStatements[`bankStatement${bankStatementMap[monthKey]}`] = new Types.ObjectId(mediaId);
      }
    } else if (type === 'solidarity_obligor_identity_card_front') {
      hasSolidarityFront = true;
      if (status === 'approved') {
        solidarityFrontApproved = true;
        formattedAvalData.ine = mediaId;
      }
    } else if (type === 'solidarity_obligor_identity_card_back') {
      hasSolidarityBack = true;
      if (status === 'approved') {
        solidarityBackApproved = true;
        if (!formattedAvalData.ine) {
          formattedAvalData.ine = mediaId;
        }
      }
    } else if (documentTypeToField[type] && status === 'approved') {
      docs[documentTypeToField[type]] = new Types.ObjectId(mediaId);
    }
  });

  return {
    docs,
    bankStatements,
    formattedAvalData,
    hasSolidarityFront,
    hasSolidarityBack,
    solidarityFrontApproved,
    solidarityBackApproved,
  };
};

const createPaymentSystemClient = async (associate: any, vehicle: any, country: string) => {
  logger.info('[createPaymentSystemClient] Creating client in payment system');

  const contractNumber =
    vehicle.carNumber + (vehicle.extensionCarNumber ? `-${vehicle.extensionCarNumber}` : '');

  let region = vehicle.vehicleState?.toUpperCase();

  const createClient: CreateClient = {
    contractNumber,
    name: associate.firstName,
    lastName: associate.lastName,
    email: associate.email,
    phone: associate.phone,
    tax_system: associate.taxData?.tax_system || '616',
    region,
    rfc: associate.rfc!,
    legal_name: associate.taxData?.legal_name || `${associate.firstName} ${associate.lastName}`.toUpperCase(),
    zip: associate.postalCode?.toString()?.padStart(5, '0'),
    associateId: associate._id?.toString(),
    metadata: {},
    country: country,
    city: associate.city,
    state: associate.state,
  };

  try {
    const response = await axios.post(`${PAYMENTS_API_URL}/clients`, createClient, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    });
    return response.data?.data?.id;
  } catch (error) {
    logger.error(`[createPaymentSystemClient] Failed to create client in payment system, ${error}`);
    return null;
  }
};

export const assignAssociate: AsyncController = async (req, res) => {
  logger.info('[assignAssociate] Starting associate assignment process');
  const { personalData, documentsAnalysis, avalData, vehicleId, userId, requestId } = req.body;

  try {
    logger.info('[assignAssociate] Processing associate data');
    const {
      firstName,
      lastName,
      email,
      birthdate: birthDay,
      nationalId: curp,
      taxId: rfc,
      phone,
      street: addressStreet,
      streetNumber: exterior,
      department: interior = '',
      neighborhood: colony,
      municipality: delegation,
      postalCode,
      city,
      state,
      taxData,
      contacts,
      country: bodyCountry,
    } = personalData;

    const newAssociateId = new Types.ObjectId();
    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) {
      logger.error('[assignAssociate] Vehicle not found', { vehicleId });
      return res.status(404).send({ message: associateText.errors.vehicleNotFound });
    }
    const country = bodyCountry === Country.mx ? CountriesEnum.Mexico : CountriesEnum['United States'];

    const {
      docs,
      bankStatements,
      formattedAvalData,
      hasSolidarityFront,
      hasSolidarityBack,
      solidarityFrontApproved,
      solidarityBackApproved,
    } = processDocuments(documentsAnalysis, country, avalData);

    if (!hasSolidarityFront || !hasSolidarityBack) {
      logger.warn('[assignAssociate] Missing solidarity obligor documents');
      return res.status(400).send({
        message: '¡Faltan documentos del garante!',
      });
    }

    if (!solidarityFrontApproved || !solidarityBackApproved) {
      logger.warn('[assignAssociate] Unapproved solidarity obligor documents');
      return res.status(400).send({
        message: 'Ambos documentos del Garante deben estar aprobados.',
      });
    }

    logger.info('[assignAssociate] Creating new associate record');
    const newAssociate: any = {
      _id: newAssociateId,
      firstName: firstName?.trim(),
      lastName: lastName?.trim(),
      email: email?.toLowerCase()?.trim(),
      birthDay: birthDay?.trim(),
      curp: curp ? curp.trim() : '',
      rfc: rfc ? rfc.trim() : '',
      phone,
      addressStreet: addressStreet?.trim(),
      exterior: exterior?.toString(),
      interior: interior?.toString(),
      colony: colony?.trim(),
      delegation: delegation,
      postalCode: postalCode?.toString()?.padStart(5, '0'),
      city,
      state,
      vehiclesId: [vehicleId],
      documents: docs,
      bankStatement: bankStatements,
      avalData: formattedAvalData,
      country: country,
    };

    const associate = new Associate(newAssociate);

    logger.info('[assignAssociate] Updating vehicle information');
    vehicle.drivers.push(newAssociateId);

    vehicle.step.stepName = steps.driverAssigned.name;
    vehicle.step.stepNumber = steps.driverAssigned.number;
    vehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    vehicle.category = VehicleCategory.assigned;
    vehicle.subCategory = VehicleSubCategory.default;

    const historyData: HistoryDataProps = {
      userId: new Types.ObjectId(userId),
      step: 'CONDUCTOR ASIGNADO',
      description: '',
      time: getCurrentDateTime(),
    };

    vehicle.updateHistory.push(historyData);

    /* START OF CREATE CLIENT */
    const clientId = await createPaymentSystemClient(associate, vehicle, country);
    if (!clientId) {
      return res.status(400).send({ message: associateText.errors.clientRegisterationFailed });
    }

    logger.info('[assignAssociate] Updating associate with client information');
    associate.clientId = clientId;
    associate.tax_system = taxData?.tax_system || '616';
    associate.use_cfdi = taxData?.use_cfdi || 'G03';

    associate.legal_name =
      taxData?.legal_name || `${associate.firstName} ${associate.lastName}`.toUpperCase();

    associate.contacts = contacts || [];

    if (country === CountriesEnum['United States']) {
      logger.info('[assignAssociate] Processing US-specific associate metadata');
      const associateUSMetadata = {
        associate: associate._id,
        city: associate.city,
        state: associate.state,
        documents: docs,
        bankStatement: bankStatements,
        contacts: personalData?.contactsUS || [],
        ssn: personalData?.ssn,
        rideShareTotalRides: personalData?.rideShareTotalRides || 0,
        avgEarningPerWeek: personalData?.avgEarningPerWeek || 0,
        mobilityPlatforms: ['Uber'],
        termsAndConditions: personalData?.termsAndConditions,
        dataPrivacyConsentForm: personalData?.dataPrivacyConsentForm,
      };
      const associateUS = new AssociateUS(associateUSMetadata);
      await associateUS.save();
    }

    // TODO: This feature related to 'Onboarding support feature' will uncommit when gets approval
    // if (country === CountriesEnum.Mexico) {
    //   // create customer acknowledgement record
    //   const custAck = new CustomerAcknowledgement({
    //     associateId: newAssociateId,
    //     stockVehicleId: vehicleId,
    //     vehicleMake: vehicle.brand,
    //     vehicleModel: vehicle.model,
    //     fullName: `${associate.firstName} ${associate.lastName}`,
    //     readBenfitsOffered: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readInsuranceCoverage: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readMaintenance: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readTermConditions: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readTheft: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readAccident: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readPayments: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     pointsAcknowledement: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //   });
    //   custAck.save();

    //   // send whatsapp
    //   await sendOnboardingSupport({
    //     phone: associate.phone.toString(),
    //     type: 'onboardingSupport',
    //   });
    //   // send email
    //   await sendOnboardingSupportEmailToCustomer({
    //     customerEmail: associate.email,
    //     subject: 'Tu aplicación está casi lista – Sigue estos pasos',
    //     url: ONBOARDING_SUPPORT_URL,
    //   });
    // }

    logger.info('[assignAssociate] Saving associate and vehicle records');
    await associate.save();
    await vehicle.save();
    await AdmissionRequestMongo.updateOne(
      { _id: requestId },
      { $set: { convertedToAssociate: true, associateId: newAssociateId } }
    );

    logger.info('[assignAssociate] Successfully completed associate assignment');
    return res.status(200).send({ message: associateText.success.associateCreated });
  } catch (error: any) {
    logger.error(`[assignAssociate] Error occurred while creating associate', ${error}`);
    return res.status(400).send({ message: associateText.errors.associateNotCreated, error: error.message });
  }
};

export const updateAssociatePicture: AsyncController = async (req, res) => {
  const id = req.params.id;
  if (!id) return res.status(401).send({ message: genericMessages.errors.missingId });

  const picture: Express.Multer.File | undefined = req.file;
  try {
    const associate = await Associate.findById(id);
    if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

    if (!picture) return res.status(401).send({ message: genericMessages.errors.missingFile });
    const vehicle = await StockVehicle.findById(req.body.vehicleId);
    if (!vehicle) return res.status(401).send({ message: stockVehiclesText.errors.vehicleNotFound });

    if (associate.picture) {
      const oldPicture = await Document.findById(associate.picture);
      if (oldPicture) {
        await deleteFileFromS3(oldPicture.path);
        await oldPicture.delete();
      }
    }

    const removeSpacesFileName = removeEmptySpacesNameFile(picture);
    const doc = new Document({
      originalName: picture.originalname,
      path: `associate/${vehicle.carNumber}/${associate.email}/picture/${removeSpacesFileName}`,
      associateId: associate._id,
    });

    await doc.save();

    await uploadFile(
      picture,
      removeSpacesFileName,
      `associate/${vehicle.carNumber}/${associate.email}/picture/`
    );

    associate.picture = doc._id;

    vehicle.updateHistory.push({
      userId: req.userId.userId,
      step: `FOTO ACTUALIZADA DEL CONDUCTOR: `,
      description: `${associate.email}`,
    });

    await vehicle.save();
    await associate.save();

    //send associate data to SQS for wallet application access
    //await sendConfimClientDataToSQS(JSON.stringify(associate));

    return res.status(200).send({ message: 'Imagen actualizada', associate });
  } catch (error) {
    console.log('[UPDATE ASSOCIATE PICTURE]', error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const getAssociate: AsyncController = async (req, res) => {
  const id = req.params.id;

  if (!id) return res.status(404).send({ message: 'ID No proporcionado' });
  const associate = await Associate.findById(id, { password: 0 }).populate('vehiclesId');

  if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

  return res.status(200).send({ message: 'Asociado encontrado', associate });
};

export const getAllAssociate: AsyncController = async (req, res) => {
  const { userId: adminId } = req.userId;
  console.log(adminId, 'adminId');
  const user = await getUserData(adminId);

  const allowedRoles = ['administrador', 'superadmin'];
  if (!adminId || !user || !allowedRoles.includes(user.role))
    return res.status(401).send({ message: associateText.errors.notAutorized });

  const allAssociates = await Associate.find().populate('vehiclesId').sort({ createdAt: -1 }).lean();

  console.time('associates');
  const associates = await Promise.all(
    allAssociates.map(async (associate) => {
      const mainContract = await MainContractSchema.findOne({
        stockId: { $in: associate.vehiclesId },
        associatedId: associate._id,
      }).sort({ createdAt: -1 });

      return {
        ...associate,
        mainContract,
      };
    })
  );
  console.log('------------------------------------');
  console.log('associates', associates.slice(-5), associates.length);
  console.timeEnd('associates');

  console.log('------------------------------------');

  const filteredAssociates = associates.filter((associate) => !!associate.mainContract);

  console.log('filteredAssociates', filteredAssociates.slice(-5), filteredAssociates.length);

  // const associates2 = await Promise.all(
  //   allAssociates.reduce((acc, associate) => {
  //     acc.push(
  //       (async () => {
  //         const mainContract = await MainContractSchema.findOne({
  //           stockId: { $in: associate.vehiclesId },
  //           associatedId: associate._id,
  //         }).sort({ createdAt: -1 });

  //         if (mainContract) {
  //           console.log('allPayments', mainContract?.allPayments?.slice(-5));
  //           return {
  //             ...associate,
  //             mainContract,
  //           };
  //         }

  //         // Retorna undefined si no hay mainContract
  //         return undefined;
  //       })()
  //     );
  //     return acc;
  //   }, [])
  // );

  // console.log('associates', associates);

  return res.status(200).send({
    message: `${associateText.success.getAllAssociates} ${allAssociates.length}`,
    allAssociates,
  });
};

interface AssociateProps {
  associateId: Types.ObjectId;
  documents: string[];
  phone: number;
  city: string;
  address: {
    zip: number;
    street: string;
    exteriorNumber: string;
    interiorNumber?: string;
  };
}

export const editAssociate: AsyncController = async (req, res) => {
  const { associateId, documents, phone, city, address }: AssociateProps = req.body;

  if (!associateId) return res.status(400).send({ message: 'Se requiere el id de usuario' });

  try {
    let associate = await Associate.findById(associateId);

    if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

    const fieldsToUpdate: { key: string; value: any }[] = [
      { key: 'phone', value: phone },
      { key: 'city', value: city },
      { key: 'documents', value: documents },
      { key: 'address.zip', value: address?.zip },
      { key: 'address.street', value: address?.street },
      { key: 'address.exteriorNumber', value: address?.exteriorNumber },
      { key: 'address.interiorNumber', value: address?.interiorNumber },
    ];

    const setNestedValue = (obj: any, keys: string[], value: any) => {
      const lastKey = keys.pop();
      for (const key of keys) {
        obj = obj[key] || {};
      }
      if (lastKey) {
        obj[lastKey] = value;
      }
    };

    for (const field of fieldsToUpdate) {
      const { key, value } = field;
      if (value !== undefined) {
        const nestedKeys = key.split('.');
        setNestedValue(associate, nestedKeys, value);
      }
    }

    await associate.save();

    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

export const removeActiveAssociate: AsyncController = async (req, res) => {
  const { id } = req.body;
  const { userId: adminId } = req.userId;

  const userAdmin = await User.findById(adminId);
  if (!userAdmin || userAdmin.role !== 'administrador')
    return res.status(401).send({ message: associateText.errors.notAutorized });

  if (!id) return res.status(400).send({ message: 'ID no proporcionado' });

  try {
    const user = await Associate.findById(id);

    if (!user) return res.status(404).send({ message: associateText.errors.associateNotFound });

    user.active = false;
    user.save();

    return res.status(200).send({ message: 'Asociado cambiado a inactivo satisfactoriamente' });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: 'Hubo un error', error });
  }
};

export const updateAssociateIne: AsyncController = async (req, res) => {
  const associateId = req.params.id;

  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send({ message: 'Conductor no encontrado' });
  const { carNumber, isEdit } = req.body;

  if (!carNumber) return res.status(404).send({ message: 'Numero del vehiculo requerido' });

  const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

  const ineFront = 'ineFront' in files ? files.ineFront[0] : undefined;
  const ineBack = 'ineBack' in files ? files.ineBack[0] : undefined;

  // const removeSpacesIneFront = removeEmptySpacesNameFile(ineFront);
  try {
    if (isEdit) {
      const oldIneFront = associate.documents?.ineFront
        ? await Document.findById(associate.documents?.ineFront)
        : null;
      const oldIneBack = associate.documents?.ineBack
        ? await Document.findById(associate.documents?.ineBack)
        : null;
      if (oldIneFront && ineFront) {
        associate.oldDocuments.push(oldIneFront._id);

        const removeSpacesIneFront = removeEmptySpacesNameFile(ineFront);
        oldIneFront.originalName = removeSpacesIneFront;
        oldIneFront.path = `associate/${carNumber}/${associate?.email}/ine/${removeSpacesIneFront}`;
        await oldIneFront.save();
        await associate.save();
      }
      if (oldIneBack && ineBack) {
        associate.oldDocuments.push(oldIneBack._id);
        const removeSpacesIneBack = removeEmptySpacesNameFile(ineBack);

        oldIneBack.originalName = removeSpacesIneBack;
        oldIneBack.path = `associate/${carNumber}/${associate?.email}/ine/${removeSpacesIneBack}`;
        await oldIneBack.save();
        await associate.save();
      }
    } else {
      if (ineFront) {
        const removeSpacesIneFront = removeEmptySpacesNameFile(ineFront);
        const doc = new Document({
          originalName: removeSpacesIneFront,
          path: `associate/${carNumber}/${associate?.email}/ine/${removeSpacesIneFront}`,
          associateId: associate?._id,
          vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
        });
        await doc.save();
        await Associate.updateOne({ _id: associate._id }, { $set: { 'documents.ineFront': doc._id } });
        await uploadFile(ineFront, removeSpacesIneFront, `associate/${carNumber}/${associate?.email}/ine/`);
      }
      if (ineBack) {
        const removeSpacesIneBack = removeEmptySpacesNameFile(ineBack);
        // await Document.create();
        const doc = new Document({
          originalName: removeSpacesIneBack,
          path: `associate/${carNumber}/${associate?.email}/ine/${removeSpacesIneBack}`,
          associateId: associate?._id,
          vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
        });
        await doc.save();
        await Associate.updateOne({ _id: associate._id }, { $set: { 'documents.ineBack': doc._id } });
        await uploadFile(ineBack, removeSpacesIneBack, `associate/${carNumber}/${associate?.email}/ine/`);
      }
    }

    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

export const updateAssociateDriverLicense: AsyncController = async (req, res) => {
  const associateId = req.params.id;

  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send({ message: 'Conductor no encontrado' });
  const { carNumber, isEdit } = req.body;

  if (!carNumber) return res.status(404).send({ message: 'Numero del vehiculo requerido' });

  const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

  const driverLicenseFront = 'driverLicenseFront' in files ? files.driverLicenseFront[0] : undefined;
  const driverLicenseBack = 'driverLicenseBack' in files ? files.driverLicenseBack[0] : undefined;

  // const removeSpacesIneFront = removeEmptySpacesNameFile(ineFront);
  try {
    if (isEdit) {
      const oldDriverLicenseFront = associate.documents?.driverLicenseFront
        ? await Document.findById(associate.documents?.driverLicenseFront)
        : null;
      const oldDriverLicenseBack = associate.documents?.driverLicenseBack
        ? await Document.findById(associate.documents?.driverLicenseBack)
        : null;
      if (oldDriverLicenseFront) associate.oldDocuments.push(oldDriverLicenseFront._id);
      if (oldDriverLicenseBack) associate.oldDocuments.push(oldDriverLicenseBack._id);
    }
    if (associate?.country === CountriesEnum['United States']) {
      const associateResponse = await associateServiceUS.updateDriverLicenseAssociateUS({
        associate,
        driverLicenseFront,
        driverLicenseBack,
        carNumber,
      });
      return res.status(associateResponse.status).send(associateResponse);
    }
    if (driverLicenseFront) {
      const removeSpacesdriverLicenseFront = removeEmptySpacesNameFile(driverLicenseFront);
      const doc = new Document({
        originalName: removeSpacesdriverLicenseFront,
        path: `associate/${carNumber}/${associate?.email}/driverLicenseFront/${removeSpacesdriverLicenseFront}`,
        associateId: associate?._id,
        vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
      });
      await doc.save();
      await Associate.updateOne(
        { _id: associate._id },
        { $set: { 'documents.driverLicenseFront': doc._id } }
      );
      await uploadFile(
        driverLicenseFront,
        removeSpacesdriverLicenseFront,
        `associate/${carNumber}/${associate?.email}/driverLicenseFront/`
      );
    }
    if (driverLicenseBack) {
      const removeSpacesdriverLicenseBack = removeEmptySpacesNameFile(driverLicenseBack);
      // await Document.create();
      const doc = new Document({
        originalName: removeSpacesdriverLicenseBack,
        path: `associate/${carNumber}/${associate?.email}/driverLicenseBack/${removeSpacesdriverLicenseBack}`,
        associateId: associate?._id,
        vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
      });
      await doc.save();
      await Associate.updateOne({ _id: associate._id }, { $set: { 'documents.driverLicenseBack': doc._id } });
      await uploadFile(
        driverLicenseBack,
        removeSpacesdriverLicenseBack,
        `associate/${carNumber}/${associate?.email}/driverLicenseBack/`
      );
    }
    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

export const updateAssociateCurp: AsyncController = async (req, res) => {
  const associateId = req.params.id;
  const { carNumber, isEdit } = req.body;

  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

  const curp: Express.Multer.File | undefined = req.file;
  if (!curp) return res.status(404).send({ message: documentsResponse.errors.curpRequired });
  const vehicle = await StockVehicle.findOne({ carNumber });
  if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
  const removeSpacesCurp = removeEmptySpacesNameFile(curp);
  try {
    if (isEdit) {
      const oldCurp = await Document.findById(associate.documents?.curp);

      if (oldCurp) {
        associate.oldDocuments.push(oldCurp._id);
      }

      const doc = new Document({
        originalName: removeSpacesCurp,
        path: `associate/${carNumber}/${associate.email}/curp/${removeSpacesCurp}`,
        associateId: associate?._id,
        vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
      });
      await doc.save();
      if (associate.documents) {
        associate.documents.curp = doc._id;
      }
      await associate.save();

      await uploadFile(curp, removeSpacesCurp, `associate/${carNumber}/${associate?.email}/curp/`);

      return res.status(200).send({ message: associateText.success.associateUpdated, associate });
    }

    const doc = new Document({
      originalName: removeSpacesCurp,
      path: `associate/${carNumber}/${associate.email}/curp/${removeSpacesCurp}`,
      associateId: associate?._id,
      vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
    });
    await doc.save();
    if (associate.documents) {
      associate.documents.curp = doc._id;
    }
    await associate.save();

    await uploadFile(curp, removeSpacesCurp, `associate/${carNumber}/${associate?.email}/curp/`);

    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

export const updateTaxStatus: AsyncController = async (req, res) => {
  const associateId = req.params.id;
  const { carNumber, isEdit } = req.body;

  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

  const taxStatus: Express.Multer.File | undefined = req.file;
  if (!taxStatus) return res.status(404).send({ message: documentsResponse.errors.taxStatus });
  const vehicle = await StockVehicle.findOne({ carNumber });
  if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
  const removeSpacesTaxStatus = removeEmptySpacesNameFile(taxStatus);
  try {
    if (isEdit) {
      const oldTaxStatus = await Document.findById(associate.documents?.taxStatus);

      if (oldTaxStatus) {
        associate.oldDocuments.push(oldTaxStatus._id);
      }

      const doc = new Document({
        originalName: removeSpacesTaxStatus,
        path: `associate/${carNumber}/${associate.email}/taxStatus/${removeSpacesTaxStatus}`,
        associateId: associate?._id,
        vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
      });
      await doc.save();
      if (associate.documents) {
        associate.documents.taxStatus = doc._id;
      }
      await associate.save();

      await uploadFile(
        taxStatus,
        removeSpacesTaxStatus,
        `associate/${carNumber}/${associate?.email}/taxStatus/`
      );

      return res.status(200).send({ message: associateText.success.associateUpdated, associate });
    }

    const doc = new Document({
      originalName: removeSpacesTaxStatus,
      path: `associate/${carNumber}/${associate.email}/taxStatus/${removeSpacesTaxStatus}`,
      associateId: associate?._id,
      vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
    });

    await doc.save();
    if (associate.documents) {
      associate.documents.taxStatus = doc._id;
    }
    await associate.save();

    await uploadFile(
      taxStatus,
      removeSpacesTaxStatus,
      `associate/${carNumber}/${associate?.email}/taxStatus/`
    );

    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

export const updateAddressVerification: AsyncController = async (req, res) => {
  const associateId = req.params.id;
  const { carNumber, isEdit } = req.body;

  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

  const addressVerification: Express.Multer.File | undefined = req.file;
  if (!addressVerification)
    return res.status(404).send({ message: documentsResponse.errors.addressVerification });
  const vehicle = await StockVehicle.findOne({ carNumber });
  if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
  try {
    if (isEdit) {
      const oldAddressVerification = await Document.findById(associate.documents?.addressVerification);
      if (oldAddressVerification) {
        associate.oldDocuments.push(oldAddressVerification._id);
      }
    }

    if (associate?.country === CountriesEnum['United States']) {
      const associateResponse = await associateServiceUS.updateAddressVerificationAssociateUS({
        associate,
        addressVerification,
        carNumber,
      });
      return res.status(associateResponse.status).send(associateResponse);
    }
    const removeSpacesAdressVeri = removeEmptySpacesNameFile(addressVerification);
    const doc = new Document({
      originalName: removeSpacesAdressVeri,
      path: `associate/${carNumber}/${associate.email}/addressVerification/${removeSpacesAdressVeri}`,
      associateId: associate?._id,
      vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
    });

    await doc.save();
    if (associate.documents) {
      associate.documents.addressVerification = doc._id;
    }
    await associate.save();

    await uploadFile(
      addressVerification,
      removeSpacesAdressVeri,
      `associate/${carNumber}/${associate?.email}/addressVerification/`
    );

    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

interface Docs {
  curp: Types.ObjectId;
  ineFront: Types.ObjectId;
  ineBack: Types.ObjectId;
  taxStatus: Types.ObjectId;
  addressVerification: Types.ObjectId;
  driverLicenseFront: Types.ObjectId;
  driverLicenseBack: Types.ObjectId;
}

type DocumentKeys = keyof Docs;

export const updateBankStatements: AsyncController = async (req, res) => {
  const associateId = req.params.id;
  const { carNumber } = req.body;

  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });
  // const associateVehicle = associate?.vehiclesId[0];
  const vehicle = await StockVehicle.findOne({ carNumber });
  if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

  const files = req.files as { [fieldname: string]: Express.Multer.File[] };

  if (Object.keys(files).length === 0)
    return res.status(400).send({ message: documentsResponse.errors.bankStatements });

  const documentFields = [
    { fieldName: 'ineFront', directory: 'ine', obj: 'docs' },
    { fieldName: 'ineBack', directory: 'ine', obj: 'docs' },
    { fieldName: 'curpDoc', directory: 'curp', obj: 'docs' },
    { fieldName: 'taxStatus', directory: 'taxStatus', obj: 'docs' },
    { fieldName: 'addressVerification', directory: 'addressVerification', obj: 'docs' },
    { fieldName: 'bankStatementsOne', directory: 'bankStatements', obj: 'bank' },
    { fieldName: 'bankStatementsTwo', directory: 'bankStatements', obj: 'bank' },
    { fieldName: 'bankStatementsThree', directory: 'bankStatements', obj: 'bank' },
    { fieldName: 'bankStatementsFour', directory: 'bankStatements', obj: 'bank' },
    { fieldName: 'bankStatementsFive', directory: 'bankStatements', obj: 'bank' },
    { fieldName: 'bankStatementsSix', directory: 'bankStatements', obj: 'bank' },
    { fieldName: 'driverLicenseFront', directory: 'driverLicense', obj: 'docs' },
    { fieldName: 'driverLicenseBack', directory: 'driverLicense', obj: 'docs' },
  ];
  let docs: any = {};
  let bankStatements: any = {};

  try {
    for (const field of documentFields) {
      if (field.fieldName in files) {
        const fieldConversion = field.fieldName as unknown as keyof typeof files;
        const file = files[fieldConversion] ? files[fieldConversion][0] : undefined;

        if (file) {
          const removeSpacesFileName = removeEmptySpacesNameFile(file);
          const documentPath = `associate/${vehicle.carNumber}/${associate.email}/${field.directory}/${removeSpacesFileName}`;
          const s3Path = `associate/${vehicle.carNumber}/${associate.email}/${field.directory}/`;

          const document = new Document({
            originalName: removeSpacesFileName,
            path: documentPath,
            associateId: associate,
          });
          if (field.obj === 'docs') {
            // esta validación es para que se asigne correctamente el nombre de la propiedad para guardar la curp en el objeto docs
            if (field.fieldName === 'curpDoc') {
              docs.curp = document._id;
            } else {
              docs[field.fieldName] = document._id;
            }
          }
          if (field.obj === 'bank') {
            bankStatements[field.fieldName] = document._id;
          }
          await document.save();
          await uploadFile(file, removeSpacesFileName, s3Path);

          if (associate.documents) {
            const dir = field.directory as DocumentKeys;
            associate.documents[dir] = document._id;
          }
        }
      }
    }

    vehicle.updateHistory.push({
      description: '',
      step: 'DOCUMENTOS DEL CONDUCTOR ACTUALIZADOS',
      userId: req.userId.userId,
    });

    await vehicle.save();
    return res.status(200).send({ message: associateText.errors.associateNotUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

interface SignDocs {
  contract: Types.ObjectId;
  deliveryReceipt: Types.ObjectId;
  promissoryNote: Types.ObjectId;
  warranty: Types.ObjectId;
  invoice: Types.ObjectId;
  privacy: Types.ObjectId;
  contactInfo: Types.ObjectId;
}

export type SignDocsKeys = keyof SignDocs;

export const updateSignedDocs: AsyncController = async (req, res) => {
  const associateId = req.params.id;
  const { carNumber } = req.body;

  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });
  // const associateVehicle = associate?.vehiclesId[0];
  const vehicle = await StockVehicle.findOne({ carNumber });
  if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

  const files = req.files as { [fieldname: string]: Express.Multer.File[] };

  if (Object.keys(files).length === 0)
    return res.status(400).send({ message: documentsResponse.errors.bankStatements });

  const documentFields = [
    { fieldName: 'contract', displayFieldName: 'Contrato' },
    { fieldName: 'promissoryNote', displayFieldName: 'Pagaré' },
    { fieldName: 'deliveryReceipt', displayFieldName: 'Acta de entrega' },
    { fieldName: 'warranty', displayFieldName: 'Garantia' },
    { fieldName: 'invoice', displayFieldName: 'Factura' },
    { fieldName: 'privacy', displayFieldName: 'Privacidad' },
    { fieldName: 'contactInfo', displayFieldName: 'Información de contacto' },
  ];
  // let docs: any = {};
  const docReceived = [];
  try {
    for (const field of documentFields) {
      if (field.fieldName in files) {
        docReceived.push({ fieldName: field.fieldName, displayFieldName: field.displayFieldName });
        const fieldConversion = field.fieldName as unknown as keyof typeof files;
        const file = files[fieldConversion] ? files[fieldConversion][0] : undefined;

        // This delete the old document if it exists
        const docExists = associate.signDocs?.[field.fieldName as SignDocsKeys];
        if (docExists) {
          const oldDocument = await Document.findById(docExists);
          if (oldDocument) {
            await oldDocument.remove();
          }
        }

        if (file) {
          const removeSpacesFileName = removeEmptySpacesNameFile(file);
          const documentPath = `associate/${vehicle.carNumber}/${associate.email}/${field.fieldName}/${removeSpacesFileName}`;
          const s3Path = `associate/${vehicle.carNumber}/${associate.email}/${field.fieldName}/`;

          const document = new Document({
            originalName: removeSpacesFileName,
            path: documentPath,
            associateId: associate,
            vehicleId: vehicle._id,
          });
          // docs[field.fieldName] = document._id;
          await document.save();
          await uploadFile(file, removeSpacesFileName, s3Path);

          if (associate.signDocs) {
            const dir = field.fieldName as SignDocsKeys;
            associate.signDocs[dir] = document._id;
            await associate.save();
          }
        }
      }
    }

    const docsText = docReceived.map((d, i) => {
      if (docReceived.length === 2 && i === 1) return `${d.displayFieldName}`;
      if (docReceived.length === 2 && i === 2) return ` y ${d.displayFieldName}`;
      if (i === docReceived.length - 1) return `${d.displayFieldName}`;
      return `${d.displayFieldName}, `;
    });

    const description =
      docReceived.length > 1
        ? `Se subieron los documentos ${docsText}`
        : `Se subió el documento ${docReceived[0].displayFieldName}`;

    const stepText =
      docReceived.length > 1
        ? `DOCUMENTOS FIRMADOS DEL CONDUCTOR ACTUALIZADOS`
        : `DOCUMENTO FIRMADO DEL CONDUCTOR ACTUALIZADO`;

    vehicle.updateHistory.push({
      description: description,
      step: stepText,
      userId: req.userId.userId,
    });
    await vehicle.save();
    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.associateNotUpdated, error });
  }
};

export const updateGarageImg: AsyncController = async (req, res) => {
  const { id } = req.params;

  const associate = await Associate.findById(id);

  if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

  const garage: Express.Multer.File | undefined = req.file;

  if (!garage) return res.status(404).send({ message: genericMessages.errors.missingBody });

  const removeSpacesGarage = removeEmptySpacesNameFile(garage);

  try {
    const newGarage = new Document({
      originalName: removeSpacesGarage,
      path: `associate/${associate.email}/garage/${removeSpacesGarage}`,
      associateId: associate?._id,
    });

    newGarage.save();

    await uploadFile(garage, removeSpacesGarage, `associate/${associate.email}/garage/`);

    associate.set({ 'documents.garage': newGarage._id });

    associate.save();

    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.log('[ASSOCIATE: UPDATE GARAGE IMAGE]', error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const updateDeliveredImages: AsyncController = async (req, res) => {
  const { id } = req.params;
  const { isEditable, deleteableImgs, vehicleId } = req.body;
  try {
    const associate = await Associate.findById(id);

    if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });

    const files = req.files as Express.Multer.File[];

    if (isEditable) {
      const vehicle = await StockVehicle.findById(vehicleId);
      if (!vehicle) return res.status(401).send({ message: stockVehiclesText.errors.vehicleNotFound });
      if (deleteableImgs && deleteableImgs.length > 0) {
        for (const imgId of deleteableImgs) {
          const imgToDelete = await Document.findById(imgId);
          if (imgToDelete) {
            // await deleteFile(imgToDelete.path);
            await deleteFileFromS3(imgToDelete.path);
            await Document.findByIdAndDelete(imgId);
            associate.deliveredImages = associate.deliveredImages.filter((i) => i.toString() !== imgId);
          }
        }
        await associate.save();
      }

      for (const f of files) {
        const removeEmptySpacesName = removeEmptySpacesNameFile(f);
        const doc = new Document({
          originalName: removeEmptySpacesName,
          path: `associate/${vehicle.carNumber}/${associate.email}/deliveredImages/${removeEmptySpacesName}`,
          associateId: associate._id,
        });

        await doc.save();

        await uploadFile(
          f,
          removeEmptySpacesName,
          `associate/${vehicle.carNumber}/${associate.email}/deliveredImages/`
        );

        associate.deliveredImages.push(doc._id);
      }

      await associate.save();

      const deliveredImages = await mapReplaceArrayOfDocsId(associate.deliveredImages);

      vehicle.updateHistory.push({
        description: '',
        step: 'IMAGENES DE ENTREGA ACTUALIZADAS',
        userId: req.userId.userId,
      });

      return res.status(200).send({
        message: 'Eliminación/agregación de imagenes hecha correctamente',
        deliveredImages,
      });
    }

    const lastVehicle = await StockVehicle.findById(associate.vehiclesId[associate.vehiclesId.length - 1]);

    if (!lastVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    for (const f of files) {
      if (f.fieldname === 'deliveredImages[]') {
        const removeSpaces = removeEmptySpacesNameFile(f);
        const newDeliveredImg = new Document({
          originalName: removeSpaces,
          path: `associate/${lastVehicle.carNumber}/${associate.email}/delivered/${removeSpaces}`,
          associateId: associate?._id,
        });
        await uploadFile(f, removeSpaces, `associate/${associate.email}/delivered/`);
        newDeliveredImg.save();
        associate.deliveredImages.push(newDeliveredImg._id);
      }
    }
    await associate.save();
    lastVehicle.updateHistory.push({
      description: '',
      step: 'IMAGENES DE ENTREGA ACTUALIZADAS',
      userId: req.userId.userId,
    });
    await lastVehicle.save();
    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.log('[ASSOCIATE: UPDATE DELIVERED IMAGES]', error);
    return res.status(200).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const updateAssociateDataById: AsyncController = async (req, res) => {
  const associateResposne = await associateService.updateAssociate({
    id: req.params.id as any,
    body: req.body,
    files: req.files as Express.Multer.File[],
    userId: req.userId.userId,
  });
  return res.status(associateResposne.status).send({
    data: associateResposne.data,
    message: associateResposne.message,
  });
};

export const addAssociateContract: AsyncController = async (req, res) => {
  const associateId = req.params.id;
  const associate = await Associate.findById(associateId);
  if (!associate) return res.status(404).send(associateText.errors.associateNotFound);
  const stockAssociate = associate?.vehiclesId[0].toString();
  const vehicle = await StockVehicle.findById(stockAssociate);
  if (!vehicle) return res.status(400).send(stockVehiclesText.errors.vehicleNotFound);

  const files = req.files as { [fieldname: string]: Express.Multer.File[] };
  const documentFields = [
    { fieldName: 'deliveredImages[]', directory: 'deliveredImages' },
    // { fieldName: 'contract', directory: 'contract' },
    // { fieldName: 'promissoryNote', directory: 'promissoryNote' },
    // { fieldName: 'warranty', directory: 'warranty' },
    // { fieldName: 'privacy', directory: 'privacy' },
    // { fieldName: 'invoice', directory: 'invoice' },
    // { fieldName: 'contactInfo', directory: 'contactInfo' },
    // { fieldName: 'deliveryReceipt', directory: 'deliveryReceipt' },
  ];

  let docs: any = {};

  try {
    for (const field of documentFields) {
      if (!field || !field.fieldName) continue;
      if (field.fieldName in files) {
        const fieldConversion = field.fieldName as unknown as keyof typeof files;
        const file = files[fieldConversion] ? files[fieldConversion][0] : undefined;

        if (field.fieldName === 'deliveredImages[]') {
          const filesArray = files[fieldConversion] ? files[fieldConversion] : undefined;
          if (filesArray && filesArray.length > 0) {
            for (const sFile of filesArray) {
              const removeSpacesFileName = removeEmptySpacesNameFile(sFile);
              const documentPath = `associate/${vehicle.carNumber}/${associate?.email}/${field.directory}/${removeSpacesFileName}`;
              const s3Path = `associate/${vehicle.carNumber}/${associate?.email}/${field.directory}/`;

              const document = new Document({
                originalName: removeSpacesFileName,
                path: documentPath,
                associateId: associate._id,
              });
              await document.save();
              await uploadFile(sFile, removeSpacesFileName, s3Path);
              associate.deliveredImages.push(document._id);
            }
          }
        }
        if (file && field.fieldName !== 'deliveredImages[]') {
          const removeSpacesFileName = removeEmptySpacesNameFile(file);
          const documentPath = `associate/${vehicle.carNumber}/${associate?.email}/${field.directory}/${removeSpacesFileName}`;
          const s3Path = `associate/${vehicle.carNumber}/${associate?.email}/${field.directory}/`;

          const document = new Document({
            originalName: removeSpacesFileName,
            path: documentPath,
            associateId: associate._id,
          });
          docs[field.directory] = document._id;
          await document.save();
          await uploadFile(file, removeSpacesFileName, s3Path);
        }
      }
    }
    // associate.signDocs = docs;
    await associate.save();
    vehicle.step.stepName = steps.delivered.name;
    vehicle.step.stepNumber = steps.delivered.number;
    vehicle.status = VehicleStatus.active;
    vehicle.vehicleStatus = UpdatedVehicleStatus.active;
    vehicle.category = VehicleCategory.default;
    vehicle.subCategory = VehicleSubCategory.default;
    vehicle.updateHistory.push({
      step: 'VEHICULO ENTREGADO',
      description: 'Se agregaron imagenes de entrega',
      time: getCurrentDateTime(),
      userId: req.userId.userId,
    });

    console.log('isElectric', vehicle.isElectric);
    if (vehicle.isElectric) {
      // sendWhatsappMessage(associate, vehicle);
      await sendNewInstallationNotification({
        associateName: `${associate.firstName}`,
        phone: associate.phone.toString(),
      });
    }

    await vehicle.save();
    const admissionRequest = await AdmissionRequestMongo.findOne({
      'personalData.nationalId': associate.curp,
    });
    if (admissionRequest) {
      await updateStageSubStage(admissionRequest?.id, SalesFunnelStage.DELIVERED_VEHICLE, null, req.authUser);
    }
    return res.status(200).send({ message: associateText.success.associateUpdated, associate });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const unassignAssociate: AsyncController = async (req, res) => {
  const { vehicleId, associateId } = req.params;
  const associateUser = await Associate.findById(associateId);
  const stockVehicle = await StockVehicle.findById(vehicleId);
  try {
    if (stockVehicle && associateUser) {
      stockVehicle.status = 'stock';
      associateUser.active = false;

      await associateUser.save();
      await stockVehicle.save();
    }
    return res.status(200).send({ message: associateText.success.associateUnassigned, associateUser });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associateText.errors.errorAssociateUnassign, error });
  }
};

// Las siguientes funciones están comentadas en las rutas y solo son para eliminar datos
// de prueba en el desarrollo, luego las eliminaré

export const deleteAllAssociates: AsyncController = async (req, res) => {
  try {
    await Associate.deleteMany({});
    return res.status(200).send({ message: 'Todos los registros han sido eliminados.' });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: 'Error al eliminar los registros.', error });
  }
};

export const deleteSelectedAssociates: AsyncController = async (req, res) => {
  const { documentIds } = req.body;

  if (!documentIds || !Array.isArray(documentIds)) {
    return res.status(400).send({ message: 'Se requiere un array de IDs de documentos' });
  }

  try {
    const result = await Associate.deleteMany({ _id: { $in: documentIds } });

    if (result.deletedCount === 0) {
      return res.status(404).send({ message: 'No se encontraron documentos con los IDs proporcionados' });
    }

    return res.status(200).send({ message: 'Registros eliminados correctamente' });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: 'Error al eliminar los registros', error });
  }
};

export const getEmailInfo: AsyncController = async (req, res) => {
  const { email } = req.params;

  const isRegisteredEmail = await Associate.findOne({ email });
  if (isRegisteredEmail) {
    return res.status(200).send({ message: associateText.success.emailFind, isRegisteredEmail });
  } else {
    return res.status(404).send({ message: associateText.errors.emailNotFound });
  }
};

export const inactiveAssociate: AsyncController = async (req, res) => {
  const drivers = await StockVehicle.find().lean();
  if (!drivers) return res.status(404).send({ message: 'No hay conductores registrados' });
  const driversId = drivers.map((d) => d.drivers);
  const result = driversId.forEach(async (d) => {
    if (d.length > 1) {
      for (let i = 0; i < d.length - 1; i++) {
        await Associate.updateOne({ _id: d[i]._id }, { $set: { active: false } });
      }
    }
  });
  return res.status(200).send({ message: associateText.success.associateUpdated, result });
};

export const getAllAssociates: AsyncController = async (req, res) => {
  const associates = await Associate.find({ active: true }).lean();
  if (!associates) return res.status(404).send({ message: associateText.errors.associateNotFound });
  const formattedAssociates = [];
  for (const associate of associates) {
    if (associate.vehiclesId[0]) {
      const vehicle = await StockVehicle.findById(associate.vehiclesId[0]);
      if (vehicle) {
        const formattedAssociate = {
          nombre: `${associate.firstName} ${associate.lastName}`,
          email: associate.email,
          telefono: associate.phone,
          contrato: vehicle.extensionCarNumber
            ? `${vehicle.carNumber}-${vehicle.extensionCarNumber}`
            : vehicle.carNumber,
          placa: vehicle.carPlates?.plates,
        };
        formattedAssociates.push(formattedAssociate);
      }
    }
  }
  return res.status(200).send(formattedAssociates);
};

export const addUserToPayFlow: AsyncController = async (req, res) => {
  const { email } = req.body;
  if (!email) return res.status(400).send({ message: 'Se requiere un correo electrónico' });
  try {
    const associate = await Associate.findOne({ email });
    if (!associate) return res.status(404).send({ message: associateText.errors.associateNotFound });
    const associatePayments = await AssociatePayments.findOne({ associateEmail: email });
    if (!associatePayments) return res.status(404).send({ message: 'No se encontraron pagos' });
    // if (associatePayments.monexClabe)
    //   return res.status(400).send({
    //     message: 'El driver ya tiene cuenta CLABE',
    //     error: `El driver ya tiene cuenta CLABE ${associatePayments.monexClabe}`,
    //   });
    if (associatePayments.gigId)
      return res.status(400).send({
        message: 'El driver ya tiene gigId',
        error: `El driver ya tiene gigId ${associatePayments.gigId}`,
      });
    if (associatePayments.suscriptionId)
      return res.status(400).send({
        message: 'El driver ya tiene suscriptionId',
        error: `El driver ya tiene suscriptionId ${associatePayments.suscriptionId}`,
      });
    const regionToFilter = associatePayments.region;
    const region = regionToFilter === 'PBE' ? 'PUE' : regionToFilter;
    const associatedData = await Associate.findOne({ email });
    if (!associatedData) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    const { firstName, lastName, vehiclesId } = associatedData;
    const carInfo = await StockVehicle.findById(
      vehiclesId.length > 0 ? vehiclesId[0] : [vehiclesId.length - 1]
    );
    if (carInfo === null) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    const alias = carInfo.extensionCarNumber
      ? `${carInfo.carNumber}-${carInfo.extensionCarNumber}`
      : carInfo.carNumber;
    const userData = JSON.stringify({
      alias,
      currency_code: wire4Data.bankAccount.currency_code,
      email: [associatedData.email],
      name: `${firstName} ${lastName}`,
    });

    let monexClabe: string;
    let i80Clabe: null;
    let oneCarNowClabe: null;

    if (carInfo.transferredTo === 'i80-1') {
      const { clabe } = await createWire4I80BankAccount(userData);
      monexClabe = clabe;
      i80Clabe = clabe;
      oneCarNowClabe = null;
    } else {
      const { clabe } = await createWire4BankAccount(userData);
      monexClabe = clabe;
      oneCarNowClabe = clabe;
      i80Clabe = null;
    }

    await AssociatePayments.findOneAndUpdate(
      {
        associateEmail: email,
      },
      {
        monexClabe,
        i80Clabe,
        oneCarNowClabe,
      },
      {
        new: true,
      }
    );
    const gigUser = await createGigUser(monexClabe, region);
    const firstPayment = await createFirstAssociateGigPayment(email, region);
    const recurrent = await createGigRecurrentPay(region, associatedData._id);
    carInfo.updateHistory.push({
      userId: req.userId.userId,
      step: `USUARIO AGREGADO A PAYFLOW`,
      description: `La susccipción inicia el ${recurrent.startDate}`,
    });
    await carInfo.save();
    return res.status(200).send({
      message: 'Usuario agregado a PayFlow correctamente',
      clabe: monexClabe,
      gigUser: gigUser.data.client.id,
      firstPayment: firstPayment.data.data.id,
      recurrent: recurrent.data.id,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const getByMonexClabe: AsyncController = async (req, res) => {
  try {
    const { monexClabe } = req.params;

    // console.log('monexClabe', monexClabe);
    const associate = await AssociatePayments.findOne({ monexClabe })
      .populate({
        path: 'associateId',
        select: {
          rfc: 1,
          email: 1,
          firstName: 1,
        },
      })
      .populate({
        path: 'contractId',
        select: {
          allPayments: 1,
        },
      })
      .populate({
        path: 'vehiclesId',
        select: {
          carNumber: 1,
          extensionCarNumber: 1,
        },
      })
      .select({
        associateId: 1,
        contractId: 1,
        monexClabe: 1,
        gigId: 1,
        suscriptionId: 1,
        region: 1,
      });

    return res.status(200).send({ message: 'Usuario encontrado', associate });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const startPayFlowController: AsyncController = async (req, res) => {
  try {
    // const { vehicleId, associateId } = req.params;
    const { vehicleId, associateId } = req.body;
    if (!vehicleId || !associateId)
      return res.status(400).send({ message: 'Se requiere un vehicleId y associateId' });

    const payFlow = await StartPayFlow.findOne({ vehicleId, associateId });
    if (!payFlow) return res.status(404).send({ message: 'Flujo de pago por iniciar no encontrado' });

    if (payFlow.isCreated) return res.status(400).send({ message: 'Flujo de pago ya creado' });
    const associate = await Associate.findById(associateId);

    const stockVehicle = await StockVehicle.findById(vehicleId);

    if (!stockVehicle) return res.status(404).send({ message: 'Vehiculo no encontrado' });

    if (!associate) return res.status(404).send({ message: 'Asociado no encontrado' });

    // generate monex clabe

    const alias = stockVehicle.extensionCarNumber
      ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
      : stockVehicle.carNumber;

    /**
     * we only need monex flow for Mexico Vehicles and Mexico Associates/clients
     * we don't need monex flor for US Vehicles and US Associates/clients
     */
    if (stockVehicle?.country !== CountriesEnum['United States']) {
      const userData = JSON.stringify({
        alias,
        currency_code: wire4Data.bankAccount.currency_code,
        email: [associate.email],
        name: `${associate.firstName} ${associate?.lastName}`,
      });

      const { clabe } = await createWire4BankAccount(userData);
      // const { clabe } = { clabe: null };

      console.log('MONEX CLABE', clabe);

      await AssociatePayments.findOneAndUpdate({ associateId }, { monexClabe: clabe });

      await axios.patch(
        `${PAYMENTS_API_URL}/clients/${associate.clientId}`,
        {
          monexClabe: clabe,
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );
    }

    // post to payments api

    const products = [];
    if (payFlow?.rentingProduct) {
      products.push(payFlow.rentingProduct);
    }
    if (payFlow?.assistanceProduct) {
      products.push(payFlow.assistanceProduct);
    }

    const createPayFlow = {
      clientId: associate?.clientId || payFlow.clientId,
      products: products,
      downPaymentProduct: payFlow.downPaymentProduct === '' ? null : payFlow.downPaymentProduct,
      depositProduct: payFlow.depositProduct === '' ? null : payFlow.depositProduct,
      startDate: payFlow.startDate,
      endDate: payFlow.endDate,
    };

    console.log('Create payflow', createPayFlow);

    try {
      const response = await axios.post(
        `${PAYMENTS_API_URL}/subscriptions/initiate-pay-flow`,
        createPayFlow,
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );

      const startDate = revertDate(payFlow.startDate);

      stockVehicle.updateHistory.push({
        userId: req.userId.userId,
        step: `USUARIO AGREGADO A PAYFLOW`,
        description: `La susccipción inicia el ${startDate}`,
      });

      console.log('response', response.data);

      payFlow.isCreated = true;
      await payFlow.save();
      await stockVehicle.save();

      return res.status(200).send({ message: 'Flujo de pago iniciado' });
    } catch (error: any) {
      const message = error.response?.data?.message;
      console.log('error on request', error.response?.data || error.message);
      return res.status(500).send({ message: message || 'Error al crear la suscripción', error });
    }
  } catch (error: any) {
    console.log('Error xd', error.response?.data || error.message);
    return res.status(500).send({ message: error.response?.data?.message || stockVehiclesText.errors.error });
  }
};

export const getPaymentFlow: AsyncController = async (req, res) => {
  try {
    const { id: associateId } = req.params;

    const { stockId } = req.query;

    const paymentFlwo = await StartPayFlow.findOne({ associateId, stockId }).select({
      associateId: 1,
      stockId: 1,
      startDate: 1,
      endDate: 1,
      isCreated: 1,
    });

    if (!paymentFlwo) return res.status(404).send({ message: 'Flujo de pago no encontrado' });

    return res.status(200).send({ message: 'Flujo de pago encontrado', data: paymentFlwo });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const updateAssociateMonexClabe: AsyncController = async (req, res) => {
  try {
    const associate = await AssociatePayments.find({ monexClabe: { $exists: false } });
    console.log(associate.length);
    for (const a of associate) {
      const { associateEmail } = a;
      const associateData = await Associate.findOne({ email: associateEmail });
      const vehicle = await StockVehicle.findById(associateData?.vehiclesId[0]);
      if (vehicle?.transferredTo === 'i80-1') {
        console.log('i80-1', associateEmail);
        const userData = JSON.stringify({
          alias: `${associateData?.firstName} ${associateData?.lastName}`,
          currency_code: wire4Data.bankAccount.currency_code,
          email: [associateData?.email],
          name: `${associateData?.firstName} ${associateData?.lastName}`,
        });
        if (associateData) {
          const { clabe } = await createWire4I80BankAccount(userData);
          await AssociatePayments.findOneAndUpdate({ associateEmail }, { monexClabe: clabe });
        }
      } else {
        console.log('monex', associateEmail);
        const userData = JSON.stringify({
          alias: `${associateData?.firstName} ${associateData?.lastName}`,
          currency_code: wire4Data.bankAccount.currency_code,
          email: [associateData?.email],
          name: `${associateData?.firstName} ${associateData?.lastName}`,
        });
        if (associateData) {
          const { clabe } = await createWire4BankAccount(userData);
          await AssociatePayments.findOneAndUpdate({ associateEmail }, { monexClabe: clabe });
        }
      }
    }
    return res.status(200).send({ message: 'Clabe actualizado correctamente' });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const updatePaymentServiceNewMonexData: AsyncController = async (req, res) => {
  try {
    const associates = await AssociatePayments.find();
    const updatedClients: any[] = [];
    const errors: any[] = [];

    // Use Promise.all to parallelize requests instead of nested forEach
    await Promise.all(
      associates.map(async (a) => {
        try {
          const associateData = await Associate.findOne({ email: a.associateEmail });
          if (!associateData) {
            throw new Error(`Associate not found for email: ${a.associateEmail}`);
          }

          const response = await axios.patch(
            `${PAYMENTS_API_URL}/clients/${associateData.clientId}`,
            {
              monexClabe: a.monexClabe,
              metadata: {
                new_account_show_count: 0,
              },
            },
            {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            }
          );
          updatedClients.push(response.data);
        } catch (error) {
          errors.push(error);
        }
      })
    );

    if (errors.length > 0) {
      return res.status(400).send({ message: 'Some updates failed', errors, updatedClients });
    } else {
      return res.status(200).send({ message: 'Clabe actualizado correctamente', updatedClients });
    }
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const importAssociateFromAdmissionRequest: AsyncController = async (req, res) => {
  const { requestId, vehicleId, avalData, taxData, userId, step, description, city, state, rfc } = req.body;
  const avalINE: Express.Multer.File | undefined = req.file;
  console.log('requestId', requestId);
  try {
    const admissionRequest = await AdmissionRequestMongo.findById(requestId);
    if (!admissionRequest) return res.status(404).send({ message: 'Admission request not found' });
    const isAdmissionAproved = admissionRequest.status === AdmissionRequestStatus.approved;

    if (!isAdmissionAproved) return res.status(400).send({ message: 'Admission request is not approved' });

    if (admissionRequest.isImported)
      return res.status(400).send({ message: 'Admission request already imported' });

    if (!admissionRequest || !vehicleId || !avalData || !avalINE || !userId || !city || !state) {
      const missingFields = [];
      if (!admissionRequest) missingFields.push('admissionRequest');
      if (!vehicleId) missingFields.push('vehicleId');
      if (!avalData) missingFields.push('avalData');
      if (!avalINE) missingFields.push('avalINE');
      if (!userId) missingFields.push('userId');
      if (!step) missingFields.push('step');
      if (!description) missingFields.push('description');
      if (!city) missingFields.push('city');
      if (!state) missingFields.push('state');
      return res.status(400).send({ message: 'Missing required fields', missingFields });
    }
    const stockVehicle = await StockVehicle.findById(vehicleId);
    if (!stockVehicle) return res.status(404).send({ message: 'Vehicle not found' });
    if (stockVehicle.step.stepNumber !== 2)
      return res.status(400).send({ message: 'Vehicle is not in the correct step' });

    const country = admissionRequest.personalData.country === 'us' ? 'United States' : 'Mexico';
    const removeSpacesAvalINE = removeEmptySpacesNameFile(avalINE);
    const associateId = new Types.ObjectId();

    const newAvalINE = new Document({
      originalName: removeSpacesAvalINE,
      path: `associate/${admissionRequest.personalData.email}/avalINE/${removeSpacesAvalINE}`,
      associateId,
    });
    await uploadFile(
      avalINE,
      removeSpacesAvalINE,
      `associate/${admissionRequest.personalData.email}/avalINE/`
    );

    await newAvalINE.save();

    const contacts = [
      {
        name: admissionRequest.personalData.references.reference1Name,
        phone: admissionRequest.personalData.references.reference1Phone,
        kinship: admissionRequest.personalData.references.reference1Relationship,
        address: admissionRequest.personalData.references.reference1Address || '',
      },
      {
        name: admissionRequest.personalData.references.reference2Name,
        phone: admissionRequest.personalData.references.reference2Phone,
        kinship: admissionRequest.personalData.references.reference2Relationship,
        address: admissionRequest.personalData.references.reference2Address || '',
      },
    ];

    if (
      admissionRequest.personalData.references.reference3Name &&
      admissionRequest.personalData.references.reference3Phone &&
      admissionRequest.personalData.references.reference3Relationship &&
      admissionRequest.personalData.references.reference3Address
    ) {
      contacts.push({
        name: admissionRequest.personalData.references.reference3Name,
        phone: admissionRequest.personalData.references.reference3Phone,
        kinship: admissionRequest.personalData.references.reference3Relationship,
        address: admissionRequest.personalData.references.reference3Address || '',
      });
    }

    const newAssociate = new Associate({
      _id: associateId,
      requestId: admissionRequest._id,
      typeOfPreapproval: admissionRequest.typeOfPreapproval,
      firstName: admissionRequest.personalData.firstName,
      lastName: admissionRequest.personalData.lastName,
      legalName: admissionRequest.personalData.firstName + ' ' + admissionRequest.personalData.lastName,
      email: admissionRequest.personalData.email,
      birthDay: admissionRequest.personalData.birthdate,
      phone: admissionRequest.personalData.phone,
      rfc: admissionRequest.personalData.taxId || rfc,
      curp: admissionRequest.personalData.nationalId,
      tax_system: taxData?.tax_system || '616',
      addressStreet: admissionRequest.personalData.street,
      exterior: admissionRequest.personalData.streetNumber,
      interior: admissionRequest.personalData.department,
      colony: admissionRequest.personalData.neighborhood,
      delegation: admissionRequest.personalData.city,
      city: admissionRequest.personalData.city,
      state: admissionRequest.personalData.state,
      vehiclesId: [vehicleId],
      postalCode: admissionRequest.personalData.postalCode,
      avalData: {
        ...avalData,
        ine: newAvalINE._id,
      },
      country,
      active: true,
      documents: {
        ineFront: admissionRequest.documentsAnalysis.documents.filter(
          (d) => d.type === 'identity_card_front'
        )[0]?.mediaId,
        ineBack: admissionRequest.documentsAnalysis.documents.filter(
          (d) => d.type === 'identity_card_back'
        )[0]?.mediaId,
        addressVerification: admissionRequest.documentsAnalysis.documents.filter(
          (d) => d.type === 'proof_of_address'
        )[0]?.mediaId,
        driverLicenseBack: admissionRequest.documentsAnalysis.documents.filter(
          (d) => d.type === 'drivers_license_back' || d.type === 'driver_license_back'
        )[0]?.mediaId,
        driverLicenseFront: admissionRequest.documentsAnalysis.documents.filter(
          (d) => d.type === 'drivers_license_front' || d.type === 'driver_license_front'
        )[0]?.mediaId,
        garage: admissionRequest.documentsAnalysis.documents.filter((d) => d.type === 'garage_photo')[0]
          ?.mediaId,
      },
      bankStatements: {
        bankStatementOne: admissionRequest.documentsAnalysis.documents
          .filter((d) => d.type === 'bank_statement_month_1')
          .map((doc) => doc.mediaId),
        bankStatementTwo: admissionRequest.documentsAnalysis.documents
          .filter((d) => d.type === 'bank_statement_month_2')
          .map((doc) => doc.mediaId),
        bankStatementThree: admissionRequest.documentsAnalysis.documents
          .filter((d) => d.type === 'bank_statement_month_3')
          .map((doc) => doc.mediaId),
        bankStatementFour: admissionRequest.documentsAnalysis.documents
          .filter((d) => d.type === 'bank_statement_month_4')
          .map((doc) => doc.mediaId),
        bankStatementFive: admissionRequest.documentsAnalysis.documents
          .filter((d) => d.type === 'bank_statement_month_5')
          .map((doc) => doc.mediaId),
        bankStatementSix: admissionRequest.documentsAnalysis.documents
          .filter((d) => d.type === 'bank_statement_month_6')
          .map((doc) => doc.mediaId),
      },
      contacts,
      admissionRequestId: admissionRequest._id,
    });

    stockVehicle.drivers.push(newAssociate._id);
    stockVehicle.step.stepName = steps.driverAssigned.name;
    stockVehicle.step.stepNumber = steps.driverAssigned.number;
    stockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    stockVehicle.category = VehicleCategory.assigned;
    stockVehicle.subCategory = VehicleSubCategory.default;

    const historyData: HistoryDataProps = {
      userId: new Types.ObjectId(userId),
      step: step || 'CONDUCTOR ASIGNADO',
      description: description || '',
      time: getCurrentDateTime(),
    };

    stockVehicle.updateHistory.push(historyData);

    const contractNumber =
      stockVehicle.carNumber + (stockVehicle.extensionCarNumber ? `-${stockVehicle.extensionCarNumber}` : '');

    let region = stockVehicle.vehicleState.toUpperCase();
    const createClient: CreateClient = {
      contractNumber,
      name: newAssociate.firstName,
      lastName: newAssociate.lastName,
      email: newAssociate.email,
      phone: newAssociate.phone.toString(),
      tax_system: taxData?.tax_system || '616',
      region,
      rfc: newAssociate.rfc!,
      legal_name: taxData?.legal_name || `${newAssociate.firstName} ${newAssociate.lastName}`.toUpperCase(),
      zip: newAssociate.postalCode.toString().padStart(5, '0'),
      associateId: newAssociate._id.toString(),
      metadata: {},
      country: country,
      city: city,
      state: state,
    };

    if (taxData?.use_cfdi) {
      Object.assign(createClient, { use_cfdi: taxData.use_cfdi });
    }

    let clientId;

    console.log('client data', createClient);

    try {
      const response = await axios.post(`${PAYMENTS_API_URL}/clients`, createClient, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${PAYMENTS_API_KEY}`,
        },
      });
      clientId = response.data.data.id;
    } catch (error: any) {
      console.error('Error occured while registering Client on Payment Module', error.response?.data);
      return res.status(400).send({ message: associateText.errors.clientRegisterationFailed });
    }

    newAssociate.clientId = clientId;
    newAssociate.tax_system = taxData?.tax_system || '616';
    newAssociate.use_cfdi = taxData?.use_cfdi || 'G03';

    newAssociate.legal_name =
      taxData?.legal_name || `${newAssociate.firstName} ${newAssociate.lastName}`.toUpperCase();

    await newAssociate.save();
    await stockVehicle.save();
    await AdmissionRequestMongo.findByIdAndUpdate(requestId, {
      status: 'finish',
      isImported: true,
      associateId: newAssociate._id,
    });

    if (country === CountriesEnum['United States']) {
      await associateServiceUS.registerAssociateUS({
        associateData: req.body,
        files: {} as { [fieldname: string]: Express.Multer.File[] },
        newAssociateId: newAssociate._id,
        vehicle: stockVehicle,
      });
    }

    // if (country === CountriesEnum.Mexico) {
    //   // create customer acknowledgement record
    //   const custAck = new CustomerAcknowledgement({
    //     associateId: newAssociate._id,
    //     stockVehicleId: vehicleId,
    //     vehicleMake: stockVehicle.brand,
    //     vehicleModel: stockVehicle.model,
    //     fullName: `${newAssociate.firstName} ${newAssociate.lastName}`,
    //     readBenfitsOffered: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readInsuranceCoverage: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readMaintenance: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readTermConditions: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readTheft: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readAccident: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     readPayments: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //     pointsAcknowledement: {
    //       status: 'pending',
    //       timeStamp: new Date(),
    //     },
    //   });
    //   custAck.save();

    //   let phoneStr = newAssociate.phone.toString();
    //   if (!phoneStr.startsWith('+52')) {
    //     phoneStr = `+52${phoneStr}`;
    //   }
    //   // send whatsapp
    //   await sendOnboardingSupport({
    //     phone: phoneStr,
    //     type: 'onboardingSupport',
    //   });
    //   // send email
    //   await sendOnboardingSupportEmailToCustomer({
    //     customerEmail: newAssociate.email,
    //     subject: 'Tu aplicación está casi lista – Sigue estos pasos',
    //     url: ONBOARDING_SUPPORT_URL,
    //   });
    // }

    return res
      .status(200)
      .send({ message: 'Associate imported from admission request', associate: newAssociate });
  } catch (error) {
    console.error(error);
    logger.error(
      '[importAssociateFromAdmissionRequest] Error importing associate from admission request',
      error
    );
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};
