/* eslint-disable @typescript-eslint/naming-convention */
import { logger } from '@/clean/lib/logger';
import { AsyncController } from '@/types&interfaces/types';
import { processPalencaProfile, fetchPalencaData } from '@/services/palenca';
import { PalencaProfile } from '@/models/palencaProfileSchema';
import { PalencaEarningsV2 } from '@/models/palencaEarningsSchema';

export const getPalencaData: AsyncController = async (req, res) => {
  try {
    const { account_id } = req.query;

    if (!account_id || typeof account_id !== 'string') {
      logger.error(`[GET Palenca Data] - Invalid accountId: ${account_id}`);
      return res.status(400).json({ error: 'Missing or invalid account_id' });
    }

    await fetchPalencaData(account_id, false, null);

    const profileData = await PalencaProfile.findOne({ account_id });
    if (!profileData) {
      logger.error(`[GET Palenca Data] - Profile Data not found: ${account_id}`);
      return res.status(404).json({ error: 'No profile data found for this account' });
    }

    const earningsData = await PalencaEarningsV2.findOne({ account_id });
    if (!earningsData) {
      logger.error(`[GET Palenca Data] - Earnings Data not found: ${account_id}`);
      return res.status(404).json({ error: 'No earning data found for this account' });
    }
    logger.info(`[GET Palenca Data] - Profile abd Earnings Data found: ${account_id}`);
    return res.status(200).json({ platform: profileData.platform });
  } catch (error) {
    logger.error('[GET Palenca Data] Error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

export const updateProfileAndEarnings: AsyncController = async (req, res) => {
  try {
    const { account_id } = req.query;
    if (!account_id || typeof account_id !== 'string') {
      return res.status(400).json({ error: 'Missing or function parameters' });
    }
    const success = await processPalencaProfile(account_id);
    if (success) {
      return res.status(200).json({ message: 'Profile data updated successfully' });
    } else {
      return res.status(500).json({ error: 'Profile data not updated successfully' });
    }
  } catch (error) {
    logger.error('[updateProfileAndEarnings] Error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};
