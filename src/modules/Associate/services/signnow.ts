import { logger } from '@/clean/lib/logger';
import axios from 'axios';

export const getSignNowToken = async () => {
  try {
    const res = await axios.post(
      'https://api.signnow.com/oauth2/token',
      {
        username: process.env.SIGN_NOW_USERNAME,
        password: process.env.SIGN_NOW_PASSWORD,
        grant_type: 'password',
        scope: '*',
      },
      {
        headers: {
          Authorization: `Basic ${process.env.SIGN_NOW_APP_BASIC_TOKEN}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );
    return res.data.access_token;
  } catch (error: any) {
    console.error('Error occured while getting Sign Now Token: ', error);
    throw new Error('Error getting Sign Now Token');
  }
};

export async function deleteSignNowDocument(documentId: string) {
  try {
    const token = process.env.SIGN_NOW_APP_API_KEY;
    const { data } = await axios.delete(`https://api.signnow.com/document/${documentId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    });
    logger.info(`[deleteSignNowDocument]: Document deleted successfully ${documentId}`);
    return data;
  } catch (error: any) {
    const message = error.response?.data || error.message;
    const errorObj = {
      message,
      documentId,
      error: error.response.data,
    };
    logger.error(`[deleteSignNowDocument]: Error occured while deleteing document from SignNow `, {
      message: error.message,
      stack: error.stack,
    });
    return {
      success: false,
      ...errorObj,
    };
  }
}
