import { logger } from '@/clean/lib/logger';
import { AsyncController } from '../../../types&interfaces/types';
import SignNowWebhook from './model';
import Associate from '../../../models/associateSchema';
import StockVehicle from '../../../models/StockVehicleSchema';
import Document from '../../../models/documentSchema';
import { uploadFileReadable } from '../../../aws/s3';
import { getCurrentDateTime } from '../../../services/timestamps';
import { Types } from 'mongoose';
import { getDocumentFromSignNow } from './services/signnow.service';

export const receiveCompletedDocument: AsyncController = async (req, res) => {
  try {
    logger.info('[receiveCompletedDocumentSignNow] Receive Completed Document Start');
    const signNow = new SignNowWebhook({ body: req.body });
    await signNow.save();

    const { document_id: documentID, document_name: documentName } = req.body?.content;

    const associate = await Associate.findOne(
      { 'digitalSignature.documentID': documentID },
      {
        vehiclesId: 1,
        email: 1,
        digitalSignature: 1,
        signDocs: 1,
      }
    );

    if (!associate) {
      logger.warn(`[receiveCompletedDocumentSignNow] Associate not found for documentID ${documentID}`);
      return res.status(404).json({ message: 'Associate not found' });
    }

    if (associate?.digitalSignature.documentID !== documentID) {
      logger.warn(
        `[receiveCompletedDocumentSignNow] Document ID does not match for associateId ${associate?.id} `
      );
      return res.status(404).json({ message: 'Document ID does not match' });
    }

    const stockId = associate.vehiclesId[associate.vehiclesId.length - 1];
    const stockVehicle = await StockVehicle.findById(stockId, {
      carNumber: 1,
      updateHistory: 1,
      digitalSignature: 1,
      step: 1,
    });

    if (!stockVehicle) {
      logger.warn(`[receiveCompletedDocumentSignNow] Stock Vehicle not found for associateId ${associate._id} and 
          documentID ${documentID}`);
      return res.status(404).json({ message: 'Stock Vehicle not found' });
    }

    logger.info(
      `[receiveCompletedDocumentSignNow] downloading document from SignNow for documentID ${documentID}`
    );
    const { pdfStream, documentPath, removeSpacesFileName } = await getDocumentFromSignNow({
      documentID,
      documentName,
      associateEmail: associate.email,
      carNumber: stockVehicle.carNumber,
    });

    const document = new Document({
      originalName: removeSpacesFileName,
      path: documentPath,
      associateId: associate,
      vehicleId: stockVehicle._id,
    });
    await document.save();
    await uploadFileReadable(pdfStream, documentPath);

    logger.info(
      `[receiveCompletedDocumentSignNow] Successfully uploaded contract to S3 for documentID ${documentID}`
    );

    if (!associate.signDocs) associate.signDocs = {};
    if (associate.signDocs) {
      const field = 'contract';
      associate.signDocs[field] = document._id;
    }

    stockVehicle.updateHistory.push({
      step: 'Signed documents added automatically',
      time: getCurrentDateTime(),
      userId:
        process.env.NODE_ENV === 'development'
          ? new Types.ObjectId('64f9fca5d0a417208d2b8e78')
          : new Types.ObjectId('647f69776a98b82801ddcc45'),
    });
    await stockVehicle.save();

    // Update all participants to signed status
    if (associate?.digitalSignature?.participants) {
      associate.digitalSignature.participants.forEach((participant: any) => {
        participant.signed = true;
      });
    }
    associate.digitalSignature.signed = true;

    await Associate.updateOne(
      { _id: associate._id },
      {
        $set: {
          signDocs: associate.signDocs,
          digitalSignature: associate.digitalSignature,
        },
      }
    );
    signNow.associateId = associate._id;
    await signNow.save();

    logger.info(
      `[receiveCompletedDocumentSignNow] Successfully saved Signed Contract for associateId ${associate._id}`
    );
    return res.status(200).send({
      message: 'Completed',
    });
  } catch (error: any) {
    logger.error(
      `[receiveCompletedDocumentSignNow] Error processing document: ${error?.message || error?.response?.data}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );

    return res.status(500).json({ message: 'Something went wrong' });
  }
};

/**
 * This controller is to handle the event of a document being signed by every participant in the contract
 * for example, if it has 3 participants, this event will trigger when every participant has signed the document
 */
export const receiveDocumentSignEvent: AsyncController = async (req, res) => {
  try {
    logger.info('[receiveDocumentSignEvent] Receive Document Sign By Participant');
    const signNow = new SignNowWebhook({ body: req.body });
    await signNow.save();

    // console.log("[receiveDocumentSignEvent] SignNow webhook body: ", req.body);
    // const { document_id: documentID, document_name: documentName } = req.body?.content;

    // const signatory = Data?.signatory;

    // if (!signatory) return res.status(400).json({ message: 'Missing fields' });

    // const associate = await Associate.findOne({ 'digitalSignature.documentID': documentID })
    //   .select({
    //     email: 1,
    //   })
    //   .select('+digitalSignature')
    //   .lean();
    // // console.log('associate', associate);
    // if (!associate || !associate.digitalSignature?.documentID)
    //   return res.status(404).json({ message: 'Associate not found' });

    // const alreadySigned = signatory.filter((s: any) => s.isSigned);

    // for (const signatoryParticipant of alreadySigned) {
    //   const participant = associate.digitalSignature.participants.find(
    //     (p: any) => p.email === signatoryParticipant.emailID
    //   );

    //   if (participant) {
    //     participant.signed = true;
    //   }
    // }

    // await Associate.updateOne(
    //   { _id: associate._id },
    //   {
    //     $set: {
    //       'digitalSignature.participants': associate.digitalSignature.participants,
    //     },
    //   }
    // );

    // weetrust.associateId = associate._id;
    // await weetrust.save();

    console.log('[END] Receive Document Sign By Participant');
    return res.status(200).send({
      message: 'Document signed by participant',
    });
  } catch (error: any) {
    console.log('full error', error);
    return res.status(500).json({ message: 'Something went wrong' });
  }
};
