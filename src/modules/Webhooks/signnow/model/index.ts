import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../../../../services/timestamps';

const signNowWebhook = new Schema({
  body: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
  },
});

signNowWebhook.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const SignNowWebhook = model('SignNowWebhook', signNowWebhook);

export default SignNowWebhook;
