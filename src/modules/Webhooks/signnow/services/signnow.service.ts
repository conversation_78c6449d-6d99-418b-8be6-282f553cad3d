import { Readable } from 'stream';
import axios from 'axios';
import { removeEmptySpacesNameFile } from '@/services/removeEmptySpaces';

interface IGetDocumentFromSignNow {
  documentName: string;
  documentID: string;
  associateEmail: string;
  carNumber: string;
}

export const getDocumentFromSignNow = async ({
  documentID,
  documentName,
  associateEmail,
  carNumber,
}: IGetDocumentFromSignNow) => {
  const signNowToken = process.env.SIGN_NOW_APP_API_KEY;
  const signNowUrl = `https://api.signnow.com/document/${documentID}/download?type=collapsed`;
  const pdfBytes = await axios.get(signNowUrl, {
    responseType: 'arraybuffer',
    headers: {
      Authorization: `Bearer ${signNowToken}`,
      Accept: 'application/pdf',
    },
  });

  const pdfStream = Readable.from(pdfBytes.data);
  const fileMock = {
    originalname: documentName + '.pdf',
  } as Express.Multer.File;
  const removeSpacesFileName = removeEmptySpacesNameFile(fileMock);
  const documentPath = `associate/${carNumber}/${associateEmail}/contract/${removeSpacesFileName}`;

  return {
    pdfStream,
    documentPath,
    removeSpacesFileName,
  };
};
