import { logger } from '@/clean/lib/logger';
import { fcmNotificationService } from '@/modules/FirebaseCloudMessaging/services/fcmNotification.service';

export interface HomeVisitAppointmentScheduledNotification {
  userId: string;
  date: string;
  time: string;
  meetingLink: string;
  rescheduleAppointmentLink: string;
}

export interface HomeVisitAppointmentCancelNotification {
  userId: string;
  rescheduleAppointmentLink: string;
}

export interface HomeVisitAppointmentSchedulingLinkNotification {
  userId: string;
  appointmentScheduleLink: string;
}

export async function sendHomeVisitAppointmentSchedulingLinkFCMNotification({
  userId,
  appointmentScheduleLink,
}: HomeVisitAppointmentSchedulingLinkNotification): Promise<void> {
  try {
    await fcmNotificationService.sendNotificationToAssociateById(userId, {
      title: '¡Felicidades! \uD83C\uDF89',
      body: `¡Tu solicitud ha sido pre-aprobada! \uD83D\uDE97\u2728\n
      El siguiente paso es muy sencillo: agenda tu visita domiciliaria virtual.\n
      \uD83D\uDCDD: Información importante:\n
      Las visitas tienen una duración aproximada de 15 a 20 minutos, aunque los horarios disponibles son de 30 minutos.\n
      Puedes agendar tu cita de lunes a viernes, de 9:00 a.m. a 6:00 p.m.\n
      Los espacios son limitados, así que te recomendamos agendar lo antes posible para asegurar tu lugar.\n
      Es indispensable que te encuentres en tu domicilio durante la visita.\n
      ¡Estamos listos para ayudarte a completar este último paso! \u2705\n
      \uD83D\uDC49 Agenda tu visita aquí:  ${appointmentScheduleLink}`,
      data: {
        type: JSON.stringify({
          operation: 'home_visit_appointment',
          action: 'scheduling_link',
        }),
      },
    });
    logger.info(
      `[sendHomeVisitAppointmentSchedulingLinkFCMNotification] - Notification sent about home visit appointment scheduling link to userId ${userId}`
    );
  } catch (error) {
    logger.error(
      `[sendHomeVisitAppointmentSchedulingLinkFCMNotification] - Error sending notification about home visit appointment scheduling link to userId ${userId}`,
      error
    );
  }
}

// Send notification on OCN Mobile application about home visit appointment scheduled
export async function sendHomeVisitAppointmentScheduledFCMNotification({
  userId,
  date,
  time,
  meetingLink,
  rescheduleAppointmentLink,
}: HomeVisitAppointmentScheduledNotification): Promise<void> {
  try {
    await fcmNotificationService.sendNotificationToAssociateById(userId, {
      title: 'Cita de visita a domicilio confirmada',
      body: `Tu visita a domiciliaria ha sido confirmada! \uD83C\uDF89\n
      Fecha: ${date}\n
      Hora: ${time}\n
      \u26A0\uFE0F Documentos necesarios:\n
      \u2705 INE (identificatión oficial)\n
      \u2705 Propietarios: Comprobante de domicilio + Recibo de predial o Certificado de residencia (CDMX).\n
      \u2705 Inquilinos: Referencias de vecinos + Comprobante de domicilio + INE del propietario o Certificado de residencia (CDMX) \n
      \uD83D\uDCF7 Fotos: 3 ángulos distintos de la fachada donde aparezcas\n
      \uD83D\uDCC5 Duración: 15-20 minutos\n
      \uD83C\uDFE0 Importante: Estar en tu domicilio durante la cita\n
      \uD83D\uDCAC Enlace de reunión: ${meetingLink}\n
      \uD83D\uDCC5 Reprogramación: ${rescheduleAppointmentLink}\n
      ¡Contáctanos en caso de que tengas alguna duda!`,
      data: {
        type: JSON.stringify({
          operation: 'home_visit_appointment',
          action: 'scheduled',
        }),
      },
    });
    logger.info(
      `[sendHomeVisitAppointmentScheduledFCMNotification] - Notification sent about home visit appointment creation to userId ${userId}`
    );
  } catch (error) {
    logger.error(
      `[sendHomeVisitAppointmentScheduledFCMNotification] - Error sending notification about home visit appointment to userId ${userId}`,
      error
    );
  }
}

export async function sendHomeVisitAppointmentCancelFCMNotification({
  userId,
  rescheduleAppointmentLink,
}: HomeVisitAppointmentCancelNotification): Promise<void> {
  try {
    await fcmNotificationService.sendNotificationToAssociateById(userId, {
      title: 'Cita de visita a domicilio cancelada',
      body: `!Hola!\n
      Notamos que cancelaste la visita a domicilio que habías programado. Esperamos que te encuentres bien.\n
      Reprogramar fácilmente una nueva haciendo clic en el siguiente enlace: ${rescheduleAppointmentLink}\n
      ¡Estamos aquí para ayudarte!`,
      data: {
        type: JSON.stringify({
          operation: 'home_visit_appointment',
          action: 'cancelled',
        }),
      },
    });
    logger.info(
      `[sendHomeVisitAppointmentCancelFCMNotification] - Notification sent about home visit appointment cancellation to userId ${userId}`
    );
  } catch (error) {
    logger.error(
      `[sendHomeVisitAppointmentCancelFCMNotification] - Error sending notification about home visit appointment cancellation to userId ${userId}`,
      error
    );
  }
}
