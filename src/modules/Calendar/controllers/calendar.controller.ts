import { Request, Response } from 'express';
import { createScheduleDto, updateScheduleDto } from '../dtos/schedules.dto';
import { calendarService } from '../services/calendar.service';
import {
  cancelAppointmentDto,
  createAppointmentDto,
  homeVisitorChangeAppointmentDto,
  statusAppointmentDto,
} from '../dtos/appointment.dto';
import { AppointmentStatus } from '@/models/appointment';
import { updateStageSubStage } from '@/clean/domain/usecases';
import { SalesFunnelStage } from '@/constants';

export const getSchedule = async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const serializedSchedule = await calendarService.getSchedule(userId);
    res.status(200).json(serializedSchedule);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const createSchedule = async (req: Request, res: Response) => {
  try {
    const validatedData = createScheduleDto.parse(req.body);
    const userId = req.params.userId;
    const serializedSchedule = await calendarService.createSchedule({ ...validatedData, user: userId });
    res.status(201).json(serializedSchedule);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const updateSchedule = async (req: Request, res: Response): Promise<void> => {
  try {
    const validatedData = updateScheduleDto.parse(req.body);
    const scheduleId = req.params.scheduleId;
    const serializedSchedule = await calendarService.updateSchedule(validatedData, scheduleId);
    res.status(201).json(serializedSchedule);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const getAvailableSlots = async (req: Request, res: Response): Promise<void> => {
  try {
    const clientTimezone = req.query.timezone as string;
    if (!clientTimezone) throw new Error('Client timezone is required');
    const date = req.params.date;
    const admissionRequestId = req.params.requestId;
    const serializedSchedule = await calendarService.getAvailableSlots(
      admissionRequestId,
      date,
      clientTimezone
    );
    res.status(200).json(serializedSchedule);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const createAppointment = async (req: Request, res: Response): Promise<void> => {
  try {
    const validatedData = createAppointmentDto.parse(req.body);
    const slot = await calendarService.bookSlot(validatedData);
    await updateStageSubStage(
      validatedData.admissionRequestId,
      SalesFunnelStage.SCHEDULED_HOME_VISIT,
      null,
      undefined
    );
    res.status(201).json(slot);
  } catch (error) {
    throw error;
  }
};

export const getAppointmentsByUserId = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.userId;

    const selectedUsers = req.query.selectedUsers;
    let selectedUsersIds: string[] = [];
    if (selectedUsers) {
      selectedUsersIds = (selectedUsers as string).split(',');
    }

    const appointments = await calendarService.getAppointmentsByUserId(userId, selectedUsersIds);
    res.status(200).json(appointments);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const statusChangeAppointment = async (req: Request, res: Response): Promise<void> => {
  try {
    const validatedData = statusAppointmentDto.parse(req.body);
    const appointment = await calendarService.statusChangeAppointment({
      ...validatedData,
      status: validatedData.status as AppointmentStatus,
      entity: req?.userReq?.userId as any,
    });
    res.status(201).json(appointment);
  } catch (error: any) {
    throw error;
  }
};

export const getAppointmentForDriverWebApp = async (req: Request, res: Response): Promise<void> => {
  try {
    const requestId = req.params.requestId;
    const clientTimezone = req.query.timezone as string;
    if (!clientTimezone) throw new Error('Client timezone is required');
    const appointment = await calendarService.getAppointmentForDriverWebApp(requestId, clientTimezone);
    res.status(200).json(appointment);
  } catch (error) {
    throw error;
  }
};

export const cancelAppointment = async (req: Request, res: Response): Promise<void> => {
  try {
    const validatedData = cancelAppointmentDto.parse(req.body);
    const appointment = await calendarService.cancelAppointment(validatedData);
    res.status(200).json(appointment);
  } catch (error) {
    throw error;
  }
};

export const getAppointment = async (req: Request, res: Response): Promise<void> => {
  try {
    const requestId = req.params.admissionRequestId;
    const appointment = await calendarService.getAppointment(requestId);
    res.status(200).json(appointment);
  } catch (error) {
    throw error;
  }
};

export const getUsersWithSameAvailableSlot = async (req: Request, res: Response): Promise<void> => {
  try {
    const slotId = req.params.slotId;
    const usersWithSameAvailableSlot = await calendarService.getUsersWithSameAvailableSlot(slotId);
    res.status(200).json(usersWithSameAvailableSlot);
  } catch (error) {
    throw error;
  }
};

export const homeVisitorChangeApointment = async (req: Request, res: Response): Promise<void> => {
  try {
    const validatedData = homeVisitorChangeAppointmentDto.parse(req.body);
    const appointment = await calendarService.homeVisitorChangeApointment({
      ...validatedData,
      userId: req?.userReq?.userId as any,
    });
    res.status(200).json(appointment);
  } catch (error: any) {
    throw error;
  }
};
