import puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';
import { VehicleState } from '@/models/StockVehicleSchema';
import { logger } from '@/clean/lib/logger';
import { format, parse } from 'date-fns';
import {
  SpanishMonths,
  VIOLATION_LINK_WITH_AMOUNT,
  VIOLATION_LINK_WITH_AMOUNT_LOGIN_EMAIL,
  VIOLATION_LINK_WITH_AMOUNT_LOGIN_PASSWORD,
  VIOLATION_LINK_WITH_AMOUNT_PLATE_PAGE,
  VIOLATION_LINK_WITH_AMOUNT_HOME_PAGE,
  VIOLATION_LINK_FOR_MTY,
  VIOLATION_LINK_FOR_GDL,
  VIOLATION_LINK_FOR_PBE,
} from '@/constants';
import {
  FechedViolationDetailsGDL,
  FechedViolationDetailsPBE,
  InfraccionData,
} from '../types/VehicleViolation.types';

export enum ScrapedStatus {
  // CDMX Scraped Statuses
  pagada = 'pagada',
  pendiente_por_pagar = 'pendiente por pagar',

  // GDL Scraped Statuses
  no_pagado = 'no pagado',
}

export enum ViolationStatus {
  paid = 'paid',
  unpaid = 'unpaid',
  unknown = 'unknown',
}

class VehicleViolationFetcherService {
  async fetchVehicleViolationCDMX(plate: string): Promise<InfraccionData[] | null> {
    try {
      plate = plate.replace(/[\s-]/g, '').toUpperCase();
      logger.info(`[VehicleViolation] - fetchVehiceAmountViolation for plate ${plate}`);

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
      const page = await browser.newPage();

      logger.info(`[VehicleViolation] - Navigating to login page`);
      await page.goto(VIOLATION_LINK_WITH_AMOUNT);

      logger.info(`[VehicleViolation] - fetchVehiceAmountViolation Typing login credentials`);
      await page.waitForSelector('#frmLogin\\:txtCorreo');
      await page.type('#frmLogin\\:txtCorreo', VIOLATION_LINK_WITH_AMOUNT_LOGIN_EMAIL);
      await page.waitForSelector('#frmLogin\\:txtPassword');
      await page.type('#frmLogin\\:txtPassword', VIOLATION_LINK_WITH_AMOUNT_LOGIN_PASSWORD);

      logger.info(`[VehicleViolation] - fetchVehiceAmountViolation Attempting to log in`);
      await Promise.all([
        page.waitForNavigation({ waitUntil: 'networkidle0' }),
        page.click('#frmLogin\\:btnIngresar'),
      ]);

      if (page.url() === VIOLATION_LINK_WITH_AMOUNT_HOME_PAGE) {
        logger.info(`[VehicleViolation] - fetchVehiceAmountViolation Login successful for plate ${plate}`);

        const newPage = await browser.newPage();
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Opening violation page for plate ${plate}`
        );
        await newPage.goto(`${VIOLATION_LINK_WITH_AMOUNT_PLATE_PAGE}${plate}/`, {
          waitUntil: 'networkidle0',
        });

        const extractData = async (tabId: string, type: string): Promise<InfraccionData[]> => {
          logger.info(
            `[VehicleViolation] - fetchVehiceAmountViolation Extracting ${type} data for plate ${plate}`
          );
          return newPage.evaluate((id: any) => {
            const cards = document.querySelectorAll(`#${id} .card`);
            const results: InfraccionData[] = [];

            cards.forEach((card) => {
              const result: InfraccionData = {};

              const ths = card.querySelectorAll('thead tr th');
              ths.forEach((th) => {
                const text = (th as HTMLElement).innerText;
                if (text.includes('Infracción:'))
                  result.infraccion = th.querySelector('span')?.innerText.trim();
                if (text.includes('Monto en pesos:'))
                  result.montoEnPesos = th.querySelector('span')?.innerText.trim();
                if (text.includes('PAGADA')) result.status = th.querySelector('span')?.innerText.trim();
                if (text.includes('PENDIENTE POR PAGAR'))
                  result.status = th.querySelector('span')?.innerText.trim();
              });

              const dateThs = card.querySelectorAll('.card-body table thead tr th');
              dateThs.forEach((th) => {
                const strong = th.querySelector('strong');
                if (strong?.innerText.includes('Fecha infracción:')) {
                  result.fechaInfraccion = (th as HTMLElement).innerText
                    .replace('Fecha infracción: ', '')
                    .trim();
                }
              });
              results.push(result);
            });

            return results;
          }, tabId);
        };

        const pendingData = await extractData('Tokyo', 'pending');
        const parsedPending = pendingData.map((entry) => ({
          ...entry,
          fechaInfraccion: entry.fechaInfraccion ? this.parseSpanishDate(entry.fechaInfraccion) : undefined,
        }));
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Found ${parsedPending.length} pending violations for plate ${plate}`
        );

        const paidData = await extractData('Paris', 'paid');
        const parsedPaid = paidData.map((entry) => ({
          ...entry,
          fechaInfraccion: entry.fechaInfraccion ? this.parseSpanishDate(entry.fechaInfraccion) : undefined,
        }));
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Found ${parsedPaid.length} paid violations for plate ${plate}`
        );

        const results: InfraccionData[] = [...parsedPending, ...parsedPaid];
        results.forEach((result) => {
          const scrapedStatus = result?.status?.trim().toLowerCase();
          let status = ViolationStatus.unknown;
          if (scrapedStatus === ScrapedStatus.pagada) status = ViolationStatus.paid;
          else if (scrapedStatus === ScrapedStatus.pendiente_por_pagar) status = ViolationStatus.unpaid;
          result.status = status;
        });

        await browser.close();
        return results;
      } else {
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Login failed. URL after attempt: ${page.url()} for plate ${plate}`
        );
      }

      await browser.close();
      return null;
    } catch (error: any) {
      logger.error(`[VehicleViolation] - fetchVehiceAmountViolation error for plate ${plate}`, {
        message: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  async fetchVehicleViolationMTY(plate: string): Promise<InfraccionData[] | null> {
    try {
      plate = plate.replace(/[\s-]/g, '').toUpperCase();
      logger.info(`[fetchVehiceViolationMTY] - for plate ${plate} and state ${VehicleState.mty}`);

      const response = await fetch(VIOLATION_LINK_FOR_MTY, {
        headers: {
          accept:
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8',
          'cache-control': 'max-age=0',
          'content-type': 'application/x-www-form-urlencoded',
          priority: 'u=0, i',
          'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'document',
          'sec-fetch-mode': 'navigate',
          'sec-fetch-site': 'same-origin',
          'sec-fetch-user': '?1',
          'upgrade-insecure-requests': '1',
          Referer: 'https://icvnl.gob.mx:1080/estadoctatt/edoctaconsulta',
          'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
        body: `__EVENTTARGET=&__EVENTARGUMENT=&__VIEWSTATE=LJELL5MzGT94SK1FvjSzyCp6%2B3voXmwiMZy5zmWovrp2GuXWoAAUbE1Q2o8mwoLPYs5RMS2aBugBskuLHvyl3pXqvi25dpIk%2FAL96%2F4JKMfSzjoUGdyjQjhD45FXPZXFRO%2FB92aLKCG9HnRXe1cmLlRRkUhDhrCgGiz%2F57lyCF1ffyN80Ta8f%2FiRHPw6qCYvIHgF4JOMRVEsl1ImxMPk1TFYCLy%2Fw9TNFN9RHv4s6PODKr9sayml0Q9%2FxHOLktkkRe%2BLYWS7lCxfIF3fNr5GRRx7JIqYa4EOnAmExtDwnfU3%2FsG%2BsfC3Rov3CgFMw9qkESzRGHjAk0YPbr2cV01W1eqv2rE4qxchmSXM513q4gxCWWFGcU8Fy5FBivTLyJo7jvguKbSbAydq23K91m9E7x%2B1ILWyDpIkxjSL8uuD29m3zXaFaa4fWvcHenXtIa1avSsAzNAg3hNP1guVCAe5fJprIQAtn8mUL9vNq%2Bo21sbQ8qy8kYHqlvqJ6tU%2F2UCC8dVmbWgFzV4hIXffgsOOd2TpU0BhUGYBhjm8aCYQ2Vd%2BOs1N2stpOxs7Zec32N3EYVcslVJ%2B6JEQHz3aVd5fBVPBrHpGw97wagrwW7nDx2hInR0I%2BuZx2DUmdC3QjqTub0cbXoKnTtMg7neZijqVbyragvHHw9HZxg6KOp%2FS774yYnKhqHXYVINaL1uFStmEs8JFRNjoNd8%2BADarYHCbJ0Dlqy0UoKfmjLD5R1nKhveMckIJaHQPv9aEuROzyrKbBrFvN2801pD1xNyHla%2FeMhEHzbGV2bfsJi84C7Y0Cq7TJFiI%2BOob3sHxQvJn245UN2hF50imAH8m4pG9fjmIRzyicBxj%2FSan93qQapKQPPOWPlMxUzRHwi%2FSDdOuAPspVaBLojgNqLGv81aMxIrOtQ%3D%3D&__VIEWSTATEGENERATOR=0EEECBED&__EVENTVALIDATION=ORq26H%2F475actcwSO%2BHQh8FA77sp1E8JM3sdZ21bnHY6lcjILLMQazNU%2BGA%2FOmuSkUzu5eF9X7Winq0%2FLHemnPOqE4%2BVc26SKAVLbIgEinjrj5odhzSvZFnFL6u9OPTSbaBAgMibMQb%2Bi%2BzOqQMTfFsplj6gnGBfjKTrmewBHkvmQczriQWfqiNFeGzOocg433bzt9s6g6FE8CwHO70FbfbbNnGqIlhgtFvSOe6NHm0%3D&hfCorreo=&txtPlaca=${plate}&ChkDonativo=on&g-recaptcha-response=&btnConsultar=Consultar&hfUrl=`,
        method: 'POST',
      });

      const body = await response.text();
      const $ = cheerio.load(body);
      const table = $('#gvMultas');
      const rows = table.find('tr');

      const results: InfraccionData[] = [];
      rows.each((index, row) => {
        if (index === 0) return; // Skip header row
        const cols = $(row).find('td');
        const description = $(cols[0]).text().trim();
        const dateMatch = description.match(/(\d{2}\/\d{2}\/\d{4})/);
        const date = dateMatch ? dateMatch[1] : null;
        const parsedDate = parse(date!, 'dd/MM/yyyy', new Date());
        const formattedDate = format(parsedDate, 'yyyy-MM-dd');
        let status = ViolationStatus.unknown; // there is no status column, setting default

        const rowData = {
          fechaInfraccion: formattedDate,
          montoEnPesos: $(cols[2]).text().trim().replace(/[$,]/g, ''),
          status: status,
        };
        results.push(rowData);
      });

      return results;
    } catch (error: any) {
      logger.error(`[fetchVehiceViolationMTY] - error for plate ${plate}`, {
        message: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  async fetchVehicleViolationGDL(plate: string): Promise<InfraccionData[] | null> {
    try {
      plate = plate.replace(/[\s-]/g, '').toUpperCase();
      logger.info(`[fetchVehiceViolationGDL] - for plate ${plate} and state ${VehicleState.gdl}`);

      const response = await fetch(VIOLATION_LINK_FOR_GDL, {
        headers: {
          accept: 'application/json, text/plain, */*',
          'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8',
          'content-type': 'application/json',
          'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site',
          Referer: 'https://ssim.guadalajara.gob.mx/',
          'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
        body: JSON.stringify({ placa: plate }),
        method: 'POST',
      });
      const body = await response.json();
      const results: any = [];
      body.forEach((infraccion: FechedViolationDetailsGDL) => {
        const { folio, monto, estatus, fecha } = infraccion;
        const parsedDate = new Date(fecha);
        const scrapedStatus = estatus.trim().toLowerCase();
        let status = ViolationStatus.unknown;
        if (scrapedStatus === ScrapedStatus.pagada) status = ViolationStatus.paid;
        else if (scrapedStatus === ScrapedStatus.no_pagado) status = ViolationStatus.unpaid;

        results.push({
          infraccion: folio,
          montoEnPesos: monto,
          status: status,
          fechaInfraccion: parsedDate,
        });
      });

      return results as InfraccionData[];
    } catch (error: any) {
      logger.error(`[fetchVehiceViolationGDL] - error for plate ${plate}`, {
        message: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  async fetchVehicleViolationPBE(plate: string): Promise<InfraccionData[] | null> {
    try {
      plate = plate.replace(/[\s-]/g, '').toUpperCase();
      logger.info(`[fetchVehiceViolationPBE] - for plate ${plate} and state ${VehicleState.pbe}`);

      const response = await fetch(VIOLATION_LINK_FOR_PBE, {
        headers: {
          accept: '*/*',
          'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8',
          'content-type': 'application/json; charset=utf-8',
          'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin',
          Referer: 'https://fotoinfraccion.puebla.gob.mx/',
          'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
        body: JSON.stringify({
          PtiOpcion: 2,
          PiFolioInfraccion: 0,
          PvchPlaca: plate,
          PvchSerie: '',
          PiFolioTramite: 0,
        }),
        method: 'POST',
      });
      const body = await response.json();
      const results: any = [];
      body?.infracciones.forEach((infraccion: FechedViolationDetailsPBE) => {
        const { iFolioInfraccion, mMontoPagar, vchEstatusInfraccion, dtFechaInfraccion } = infraccion;
        const parsedDate = parse(dtFechaInfraccion, 'dd/MM/yyyy HH:mm:ss', new Date());
        const scrapedStatus = vchEstatusInfraccion.trim().toLowerCase();
        let status = ViolationStatus.unknown;
        if (scrapedStatus === ScrapedStatus.pagada) status = ViolationStatus.paid;
        else if (scrapedStatus === ScrapedStatus.no_pagado) status = ViolationStatus.unpaid;

        results.push({
          infraccion: iFolioInfraccion,
          montoEnPesos: mMontoPagar,
          status: status,
          fechaInfraccion: parsedDate,
        });
      });

      return results as InfraccionData[];
    } catch (error: any) {
      logger.error(`[fetchVehiceViolationGDL] - error for plate ${plate}`, {
        message: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  private parseSpanishDate(dateStr: string): string | undefined {
    const match = dateStr.toLowerCase().match(/(\d{1,2})\s+de\s+([a-zñ]+)\s+de\s+(\d{4})/i);

    if (!match) return undefined;

    const day = match[1].padStart(2, '0');
    const month = SpanishMonths[match[2]];
    const year = match[3];

    if (!month) return undefined;

    return `${year}-${month}-${day}`;
  }
}

export const vehicleViolationFetcher = new VehicleViolationFetcherService();
