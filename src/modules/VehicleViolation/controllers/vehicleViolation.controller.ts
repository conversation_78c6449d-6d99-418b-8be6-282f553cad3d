import { vehicleViolationService } from '../services/vehicleViolation.service';
import { Request, Response } from 'express';

export const updateViolationByCarNumber = async (req: Request, res: Response) => {
  try {
    const carNumber = req.body.carNumber;
    const response = await vehicleViolationService.updateViolationByCarNumber(carNumber);
    res.status(200).json(response);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};
