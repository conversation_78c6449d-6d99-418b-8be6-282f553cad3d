export interface CapturedResponse {
  status: string;
  html: string;
  msg: string;
  fotocivicas_verifica: boolean;
  sin_derecho: boolean;
  code: string;
}

export interface HtmlResults {
  folio: string;
  fecha_de_infraccion: Date;
  situacion: string;
}

export interface InfraccionData {
  infraccion?: string;
  montoEnPesos?: string;
  fechaInfraccion?: string;
  status?: string;
}

export interface InfraccionResuts {
  pendingIntraccions: InfraccionData[];
  paidIntraccions: InfraccionData[];
}

export interface FechedViolationDetailsGDL {
  id: number;
  placa: string;
  estado_id: number;
  marca_id: null | number;
  zona: string;
  estatus: string;
  lugar: string;
  cruce: string;
  fecha: string;
  hora: string;
  cat_infraccion_id: number;
  observaciones: string;
  folio: string;
  folio_referencia: string;
  lat: string;
  lon: string;
  agente_id: number;
  enviado: boolean;
  enviado_parkimovil: boolean;
  texto_qr: string;
  created_at: null | string;
  updated_at: null | string;
  tiene_descuento: boolean;
  monto: number;
  tipo_descuento: null | string;
  cantidad_descuento: number;
  porcentaje: number;
  rubro: number;
  clave: {
    id: number;
    clave_infraccion: string;
    descripcion: string;
    fundamento: string;
    fraccion: string;
    importe: string;
    regla_descuento: string;
    clave: number;
    clave_contable_id: number;
    articulo: null | string;
    anio: string;
    vigencia: boolean;
    estatus: boolean;
    created_at: string;
    updated_at: string;
    descuento: any[];
  };
  descuentos: any[];
  recargos: any[];
  placa_curso: null | string;
  pago: {
    id: number;
    linea_captura: null | string;
    infraccion_id: number;
    monto: null | number;
    monto_sin_descuento: string;
    estatus: string;
    tipo_pago: null | string;
    tipo_tarjeta: null | string;
    fecha_pedido: null | string;
    nombre_titular: null | string;
    span_route_number: null | string;
    monto_cartera: null | number;
    monto_redondeo: null | number;
    id_cobro_odoo: null | string;
    folio_pago: null | string;
    folio_pago_odoo: null | string;
    recaudadora: null | string;
    centro: null | string;
    caja: null | string;
    auth_code: null | string;
    fecha_pago: null | string;
    enviado_odoo: boolean;
    created_at: string;
    updated_at: string;
    folio_pago_kiosco: null | string;
    isNetpayPago: boolean;
  };
}

export interface FechedViolationDetailsPBE {
  iFolioInfraccion: number;
  iIdTipoInfraccion: number;
  vchDescripcionTipoInfraccion: string;
  dtFechaInfraccion: string;
  vchHoraInfraccion: string;
  iEstatusInfraccion: number;
  vchEstatusInfraccion: string;
  mMontoMulta: number;
  mMontoPagar: number;
  iNoDiasSalario: number;
  mMontoUMA: number;
  iNoMulta: number;
  dFechaNotificacion: string;
  vchEstatusNotificacion: string;
  vchPlaca: string;
  vchSerie: string;
  vchMarca: string;
  iModelo: string;
  vchLinea: string;
  vchTipo: string;
  urlFotoVehiculo: string;
  urlFotoPlaca: string;
  vchNombrePropietario: string;
  vchRFC: string;
  tiClaveTipoPersona: number;
  vchDescTipoPersona: string;
  iRegimenFiscalCFDI: number;
  vchRegimenFiscalCFDI: string;
  vchTagUsoFiscalCFDI: string;
  vchUsoFiscalCFDI: string;
  iCodigoPostalCFDI: number;
  vchRegimenSocietario: string;
  iFolioConsulta: number;
  vchColor: string;
  vchLugarInfraccion: string;
  iLimiteVelocidad: string;
  iVelocidadDetectada: string;
  iAnioInfraccion: number;
  dtFechaPago: string;
  dtFechaVigencia: string;
  chReferencia: string;
  vchSignStatus: string;
  bIsChecked: boolean;
}
